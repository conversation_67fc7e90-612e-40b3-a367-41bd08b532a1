// Subscription Management System
// Handles client subscriptions, access control, and billing status

const fs = require('fs');
const path = require('path');

class SubscriptionManager {
    constructor() {
        this.subscriptionsFile = path.join(process.cwd(), 'config', 'subscriptions.json');
        this.subscriptions = this.loadSubscriptions();
    }

    /**
     * Load subscriptions from file
     * @returns {Object} Subscriptions data
     */
    loadSubscriptions() {
        try {
            if (fs.existsSync(this.subscriptionsFile)) {
                const data = fs.readFileSync(this.subscriptionsFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.error('Error loading subscriptions:', error);
        }
        
        // Return default structure if file doesn't exist or is corrupted
        return {
            clients: {},
            plans: {
                basic: {
                    name: "Basic Plan",
                    maxGuilds: 1,
                    features: ["basic_commands", "moderation"],
                    price: 9.99
                },
                premium: {
                    name: "Premium Plan",
                    maxGuilds: 5,
                    features: ["basic_commands", "moderation", "advanced_features", "custom_commands"],
                    price: 19.99
                },
                enterprise: {
                    name: "Enterprise Plan",
                    maxGuilds: -1, // unlimited
                    features: ["all_features"],
                    price: 49.99
                }
            }
        };
    }

    /**
     * Save subscriptions to file
     */
    saveSubscriptions() {
        try {
            const configDir = path.dirname(this.subscriptionsFile);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }
            fs.writeFileSync(this.subscriptionsFile, JSON.stringify(this.subscriptions, null, 4));
        } catch (error) {
            console.error('Error saving subscriptions:', error);
        }
    }

    /**
     * Create a new client subscription
     * @param {string} clientId - Client identifier
     * @param {Object} subscriptionData - Subscription details
     * @returns {boolean} Success status
     */
    createSubscription(clientId, subscriptionData) {
        const defaultSubscription = {
            plan: 'basic',
            status: 'active',
            createdAt: new Date().toISOString(),
            expiresAt: null,
            features: this.subscriptions.plans.basic.features,
            maxGuilds: this.subscriptions.plans.basic.maxGuilds,
            currentGuilds: 0,
            paymentStatus: 'paid',
            lastPayment: new Date().toISOString(),
            ...subscriptionData
        };

        this.subscriptions.clients[clientId] = defaultSubscription;
        this.saveSubscriptions();
        
        console.log(`✅ Created subscription for client: ${clientId}`);
        return true;
    }

    /**
     * Update client subscription
     * @param {string} clientId - Client identifier
     * @param {Object} updates - Updates to apply
     * @returns {boolean} Success status
     */
    updateSubscription(clientId, updates) {
        if (!this.subscriptions.clients[clientId]) {
            console.error(`❌ Client ${clientId} not found`);
            return false;
        }

        this.subscriptions.clients[clientId] = {
            ...this.subscriptions.clients[clientId],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.saveSubscriptions();
        console.log(`✅ Updated subscription for client: ${clientId}`);
        return true;
    }

    /**
     * Check if client subscription is active
     * @param {string} clientId - Client identifier
     * @returns {boolean} Active status
     */
    isClientActive(clientId) {
        const subscription = this.subscriptions.clients[clientId];
        if (!subscription) return false;

        // Check subscription status
        if (subscription.status !== 'active') return false;

        // Check expiration date
        if (subscription.expiresAt) {
            const expirationDate = new Date(subscription.expiresAt);
            if (expirationDate < new Date()) {
                this.updateSubscription(clientId, { status: 'expired' });
                return false;
            }
        }

        // Check payment status
        if (subscription.paymentStatus === 'failed' || subscription.paymentStatus === 'pending') {
            return false;
        }

        return true;
    }

    /**
     * Get all active clients
     * @returns {Array} Array of active client IDs
     */
    getActiveClients() {
        return Object.keys(this.subscriptions.clients).filter(clientId => 
            this.isClientActive(clientId)
        );
    }

    /**
     * Get client subscription details
     * @param {string} clientId - Client identifier
     * @returns {Object|null} Subscription details or null
     */
    getClientSubscription(clientId) {
        return this.subscriptions.clients[clientId] || null;
    }

    /**
     * Suspend client subscription
     * @param {string} clientId - Client identifier
     * @param {string} reason - Suspension reason
     * @returns {boolean} Success status
     */
    suspendClient(clientId, reason = 'Administrative action') {
        return this.updateSubscription(clientId, {
            status: 'suspended',
            suspensionReason: reason,
            suspendedAt: new Date().toISOString()
        });
    }

    /**
     * Reactivate client subscription
     * @param {string} clientId - Client identifier
     * @returns {boolean} Success status
     */
    reactivateClient(clientId) {
        const updates = {
            status: 'active',
            suspensionReason: null,
            suspendedAt: null,
            reactivatedAt: new Date().toISOString()
        };
        return this.updateSubscription(clientId, updates);
    }

    /**
     * Delete client subscription
     * @param {string} clientId - Client identifier
     * @returns {boolean} Success status
     */
    deleteClient(clientId) {
        if (!this.subscriptions.clients[clientId]) {
            console.error(`❌ Client ${clientId} not found`);
            return false;
        }

        delete this.subscriptions.clients[clientId];
        this.saveSubscriptions();
        
        console.log(`✅ Deleted subscription for client: ${clientId}`);
        return true;
    }

    /**
     * Check if client has specific feature access
     * @param {string} clientId - Client identifier
     * @param {string} feature - Feature to check
     * @returns {boolean} Access status
     */
    hasFeatureAccess(clientId, feature) {
        const subscription = this.getClientSubscription(clientId);
        if (!subscription || !this.isClientActive(clientId)) return false;

        return subscription.features.includes(feature) || subscription.features.includes('all_features');
    }

    /**
     * Check guild limit for client
     * @param {string} clientId - Client identifier
     * @returns {Object} Guild limit information
     */
    checkGuildLimit(clientId) {
        const subscription = this.getClientSubscription(clientId);
        if (!subscription) return { allowed: false, reason: 'No subscription' };

        if (subscription.maxGuilds === -1) {
            return { allowed: true, unlimited: true };
        }

        const canAdd = subscription.currentGuilds < subscription.maxGuilds;
        return {
            allowed: canAdd,
            current: subscription.currentGuilds,
            max: subscription.maxGuilds,
            remaining: subscription.maxGuilds - subscription.currentGuilds
        };
    }

    /**
     * Update guild count for client
     * @param {string} clientId - Client identifier
     * @param {number} count - New guild count
     * @returns {boolean} Success status
     */
    updateGuildCount(clientId, count) {
        return this.updateSubscription(clientId, { currentGuilds: count });
    }

    /**
     * Get subscription statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        const clients = Object.values(this.subscriptions.clients);
        const active = clients.filter(c => c.status === 'active').length;
        const suspended = clients.filter(c => c.status === 'suspended').length;
        const expired = clients.filter(c => c.status === 'expired').length;
        
        const planCounts = {};
        clients.forEach(client => {
            planCounts[client.plan] = (planCounts[client.plan] || 0) + 1;
        });

        return {
            total: clients.length,
            active,
            suspended,
            expired,
            planDistribution: planCounts
        };
    }

    /**
     * Get clients expiring soon
     * @param {number} days - Days ahead to check
     * @returns {Array} Clients expiring soon
     */
    getExpiringClients(days = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() + days);

        return Object.entries(this.subscriptions.clients)
            .filter(([clientId, subscription]) => {
                if (!subscription.expiresAt) return false;
                const expirationDate = new Date(subscription.expiresAt);
                return expirationDate <= cutoffDate && expirationDate > new Date();
            })
            .map(([clientId, subscription]) => ({
                clientId,
                expiresAt: subscription.expiresAt,
                plan: subscription.plan
            }));
    }
}

module.exports = SubscriptionManager;
