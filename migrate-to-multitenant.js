#!/usr/bin/env node

// Migration Script: Single Bot to Multi-Tenant System
// Helps migrate existing single bot setup to multi-tenant architecture

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class MigrationScript {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * Display migration banner
     */
    displayBanner() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                      Migration Script                       ║
╚══════════════════════════════════════════════════════════════╝

This script will help you migrate from a single bot setup to the
multi-tenant bot management system.
        `);
    }

    /**
     * Ask a question and return the answer
     */
    async ask(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * Check if this is a single bot setup
     */
    checkCurrentSetup() {
        console.log('\n🔍 Analyzing current setup...');
        
        const checks = {
            hasConfig: fs.existsSync('config.json'),
            hasIndex: fs.existsSync('index.js'),
            hasHandler: fs.existsSync('handler/index.js'),
            hasCommands: fs.existsSync('Commands'),
            hasEvents: fs.existsSync('events'),
            hasMultiTenant: fs.existsSync('manager/BotManager.js')
        };

        console.log(`   Config file (config.json): ${checks.hasConfig ? '✅' : '❌'}`);
        console.log(`   Main file (index.js): ${checks.hasIndex ? '✅' : '❌'}`);
        console.log(`   Handler: ${checks.hasHandler ? '✅' : '❌'}`);
        console.log(`   Commands: ${checks.hasCommands ? '✅' : '❌'}`);
        console.log(`   Events: ${checks.hasEvents ? '✅' : '❌'}`);
        console.log(`   Multi-tenant system: ${checks.hasMultiTenant ? '✅' : '❌'}`);

        if (checks.hasMultiTenant) {
            console.log('\n✅ Multi-tenant system already detected!');
            return 'already_migrated';
        }

        if (!checks.hasConfig || !checks.hasIndex) {
            console.log('\n❌ This doesn\'t appear to be a Discord bot project.');
            return 'not_bot_project';
        }

        return 'single_bot';
    }

    /**
     * Read current configuration
     */
    readCurrentConfig() {
        try {
            const configData = fs.readFileSync('config.json', 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error('❌ Error reading config.json:', error.message);
            return null;
        }
    }

    /**
     * Backup current files
     */
    async backupCurrentFiles() {
        console.log('\n💾 Creating backup of current files...');
        
        const backupDir = `backup_${Date.now()}`;
        fs.mkdirSync(backupDir, { recursive: true });

        const filesToBackup = [
            'config.json',
            'index.js',
            'handler/index.js',
            'package.json'
        ];

        for (const file of filesToBackup) {
            if (fs.existsSync(file)) {
                const backupPath = path.join(backupDir, file);
                const backupDirPath = path.dirname(backupPath);
                
                if (!fs.existsSync(backupDirPath)) {
                    fs.mkdirSync(backupDirPath, { recursive: true });
                }
                
                fs.copyFileSync(file, backupPath);
                console.log(`   ✅ Backed up: ${file} -> ${backupPath}`);
            }
        }

        console.log(`\n✅ Backup created in: ${backupDir}/`);
        return backupDir;
    }

    /**
     * Migrate configuration to multi-tenant format
     */
    async migrateConfiguration(currentConfig) {
        console.log('\n⚙️ Migrating configuration...');

        const clientId = await this.ask('Enter a client ID for your current bot (e.g., main_bot): ') || 'main_bot';
        const clientName = await this.ask('Enter a name for your current bot (e.g., Main Bot): ') || 'Main Bot';
        
        console.log('\nAvailable subscription plans:');
        console.log('  1. basic - 1 guild, basic features');
        console.log('  2. premium - 5 guilds, advanced features');
        console.log('  3. enterprise - unlimited guilds, all features');
        
        let plan = 'premium'; // Default to premium for existing bots
        const planChoice = await this.ask('Choose plan for migrated bot (1-3, default: premium): ');
        if (planChoice === '1' || planChoice.toLowerCase() === 'basic') {
            plan = 'basic';
        } else if (planChoice === '3' || planChoice.toLowerCase() === 'enterprise') {
            plan = 'enterprise';
        }

        // Create multi-tenant configuration
        try {
            const TokenManager = require('./manager/TokenManager');
            const SubscriptionManager = require('./manager/SubscriptionManager');
            
            const tokenManager = new TokenManager();
            const subscriptionManager = new SubscriptionManager();

            // Add client configuration
            const clientConfig = {
                token: currentConfig.token,
                name: clientName,
                prefix: currentConfig.prefix || '#',
                owners: currentConfig.owners || [],
                guilds: currentConfig.Guild ? [currentConfig.Guild] : [],
                features: []
            };

            const tokenSuccess = tokenManager.addClient(clientId, clientConfig);
            if (!tokenSuccess) {
                throw new Error('Failed to add client configuration');
            }

            // Create subscription
            const subscriptionData = {
                plan,
                status: 'active'
            };

            const subscriptionSuccess = subscriptionManager.createSubscription(clientId, subscriptionData);
            if (!subscriptionSuccess) {
                tokenManager.removeClient(clientId);
                throw new Error('Failed to create subscription');
            }

            console.log('   ✅ Configuration migrated successfully');
            console.log(`   Client ID: ${clientId}`);
            console.log(`   Name: ${clientName}`);
            console.log(`   Plan: ${plan}`);

            return { clientId, clientName, plan };

        } catch (error) {
            console.error('   ❌ Migration failed:', error.message);
            return null;
        }
    }

    /**
     * Update package.json scripts
     */
    updatePackageJson() {
        console.log('\n📦 Updating package.json scripts...');
        
        try {
            const packagePath = 'package.json';
            const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // Add new scripts while preserving existing ones
            const newScripts = {
                "setup": "node setup.js",
                "multi-tenant": "node multi-tenant-bot.js",
                "start-all": "node scripts/start-all.js",
                "stop-all": "node scripts/stop-all.js",
                "manage-client": "node scripts/manage-client.js",
                "add-client": "node scripts/manage-client.js add",
                "remove-client": "node scripts/manage-client.js remove",
                "client-status": "node scripts/manage-client.js status",
                "list-clients": "node scripts/manage-client.js list"
            };

            packageData.scripts = { ...packageData.scripts, ...newScripts };
            
            fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2));
            console.log('   ✅ Package.json updated with new scripts');

        } catch (error) {
            console.error('   ❌ Error updating package.json:', error.message);
        }
    }

    /**
     * Display migration results and next steps
     */
    displayResults(migrationData, backupDir) {
        console.log('\n🎉 Migration completed successfully!');
        
        if (migrationData) {
            console.log('\n📊 Migration Summary:');
            console.log(`   Client ID: ${migrationData.clientId}`);
            console.log(`   Client Name: ${migrationData.clientName}`);
            console.log(`   Subscription Plan: ${migrationData.plan}`);
        }
        
        console.log(`\n💾 Backup Location: ${backupDir}/`);
        
        console.log('\n📋 Next Steps:');
        console.log('   1. Test the migration:');
        console.log('      npm run multi-tenant --auto-start');
        console.log('');
        console.log('   2. Or start individual components:');
        console.log('      npm run start-all');
        console.log('');
        console.log('   3. Manage your bots:');
        console.log('      npm run list-clients');
        console.log('      npm run client-status');
        console.log('');
        console.log('   4. Add more clients:');
        console.log('      npm run add-client "NEW_TOKEN" new_client_id');
        console.log('');
        console.log('⚠️  Important Notes:');
        console.log('   - Your original bot will now run as a managed client');
        console.log('   - All data is preserved with client-specific isolation');
        console.log('   - You can still use the original index.js for single-bot mode');
        console.log('   - Backup files are available if you need to rollback');
    }

    /**
     * Main migration process
     */
    async run() {
        try {
            this.displayBanner();
            
            // Check current setup
            const setupType = this.checkCurrentSetup();
            
            if (setupType === 'already_migrated') {
                console.log('\nNo migration needed. Use the existing multi-tenant commands:');
                console.log('  npm run start-all');
                console.log('  npm run list-clients');
                return;
            }
            
            if (setupType === 'not_bot_project') {
                console.log('\nThis doesn\'t appear to be a Discord bot project.');
                console.log('If you want to set up a new multi-tenant system, run:');
                console.log('  npm run setup');
                return;
            }

            // Confirm migration
            const confirm = await this.ask('\nDo you want to proceed with the migration? (y/n): ');
            if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                console.log('Migration cancelled.');
                return;
            }

            // Read current configuration
            const currentConfig = this.readCurrentConfig();
            if (!currentConfig) {
                console.log('❌ Could not read current configuration. Migration aborted.');
                return;
            }

            // Create backup
            const backupDir = await this.backupCurrentFiles();

            // Migrate configuration
            const migrationData = await this.migrateConfiguration(currentConfig);
            if (!migrationData) {
                console.log('❌ Configuration migration failed. Check the backup in:', backupDir);
                return;
            }

            // Update package.json
            this.updatePackageJson();

            // Display results
            this.displayResults(migrationData, backupDir);

        } catch (error) {
            console.error('\n❌ Migration failed:', error);
        } finally {
            this.rl.close();
        }
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Multi-Tenant Bot Manager Migration

Usage: node migrate-to-multitenant.js [options]

Options:
  --help, -h        Show this help message

This script will:
  1. Analyze your current bot setup
  2. Create a backup of existing files
  3. Migrate configuration to multi-tenant format
  4. Update package.json with new scripts
  5. Preserve all existing functionality

Your original bot will become the first client in the multi-tenant system.
    `);
    process.exit(0);
}

// Run the migration script
if (require.main === module) {
    const migration = new MigrationScript();
    migration.run().catch(error => {
        console.error('❌ Migration script failed:', error);
        process.exit(1);
    });
}

module.exports = MigrationScript;
