const { MessageEmbed } = require('discord.js');
const db = require('pro.db');
const { owners } = require(`${process.cwd()}/config`);

module.exports = {
  name: 'tcrestart',
  run: async (client, message, args) => {

    if (!owners.includes(message.author.id)) return message.react('❌');
    const isEnabled = clientDb.get(`command_enabled_${module.exports.name}`);
    if (isEnabled === false) {
        return; 
    }

    const guildId = message.guild.id;

    if (clientDb.get(`Channel = [${guildId}]`)) clientDb.delete(`Channel = [${guildId}]`);
    if (clientDb.get(`Role = [${guildId}]`)) clientDb.delete(`Role = [${guildId}]`);
    if (clientDb.get(`Image = [${guildId}]`)) clientDb.delete(`Image = [${guildId}]`);
    if (clientDb.get(`Cat = [${guildId}]`)) clientDb.delete(`Cat = [${guildId}]`);
    if (clientDb.get(`menuOptions_${guildId}`)) clientDb.delete(`menuOptions_${guildId}`);
    const memberKey = `member${message.author.id}`;
    const channelKey = `channel${message.author.id}_${message.channel.id}`;

    if (clientDb.get(memberKey)) clientDb.delete(memberKey);
    if (clientDb.get(channelKey)) clientDb.delete(channelKey);


    message.react("✅");
  },
};
