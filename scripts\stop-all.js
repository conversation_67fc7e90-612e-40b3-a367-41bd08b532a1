#!/usr/bin/env node

// Stop All Bots Script
// Gracefully stops all running bot instances

const BotManager = require('../manager/BotManager');

class StopAllScript {
    constructor() {
        this.botManager = new BotManager();
    }

    /**
     * Display shutdown banner
     */
    displayBanner() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                       Stopping All Bots                     ║
╚══════════════════════════════════════════════════════════════╝
        `);
    }

    /**
     * Display current status before shutdown
     */
    displayCurrentStatus() {
        const status = this.botManager.getStatus();
        
        console.log('\n📊 Current Status:');
        console.log(`   Running Bots: ${status.runningBots}`);
        console.log(`   Active Clients: ${status.totalActiveClients}`);
        
        if (status.runningBots.length > 0) {
            console.log('\n🤖 Currently Running Bots:');
            status.runningBots.forEach(clientId => {
                console.log(`   • ${clientId}`);
            });
        } else {
            console.log('\n✅ No bots are currently running');
        }
    }

    /**
     * Stop all bots with detailed logging
     */
    async stopAllBots() {
        try {
            console.log('\n🛑 Initiating shutdown sequence...\n');
            
            const results = await this.botManager.stopAllBots();
            
            console.log('\n📋 Shutdown Results:');
            console.log(`   ✅ Successfully Stopped: ${results.success.length}`);
            
            if (results.success.length > 0) {
                results.success.forEach(clientId => {
                    console.log(`      • ${clientId}`);
                });
            }
            
            if (results.failed.length > 0) {
                console.log(`   ❌ Failed to Stop: ${results.failed.length}`);
                results.failed.forEach(clientId => {
                    console.log(`      • ${clientId}`);
                });
            }
            
            return results;
            
        } catch (error) {
            console.error('❌ Error stopping bots:', error);
            throw error;
        }
    }

    /**
     * Force stop specific bots if graceful shutdown fails
     */
    async forceStop(clientIds) {
        console.log('\n⚠️  Attempting force stop for failed bots...');
        
        for (const clientId of clientIds) {
            try {
                const bot = this.botManager.getBot(clientId);
                if (bot) {
                    await bot.destroy();
                    console.log(`   🔨 Force stopped: ${clientId}`);
                }
            } catch (error) {
                console.error(`   ❌ Force stop failed for ${clientId}:`, error.message);
            }
        }
    }

    /**
     * Main execution function
     */
    async run() {
        try {
            this.displayBanner();
            
            // Display current status
            this.displayCurrentStatus();
            
            const status = this.botManager.getStatus();
            
            if (status.runningBots === 0) {
                console.log('\n✅ No bots are running. Nothing to stop.');
                return;
            }
            
            // Confirm shutdown if not forced
            if (!process.argv.includes('--force')) {
                console.log('\n⚠️  This will stop all running bot instances.');
                console.log('   Press Ctrl+C to cancel, or wait 5 seconds to continue...');
                
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
            
            // Stop all bots
            const results = await this.stopAllBots();
            
            // Force stop any that failed
            if (results.failed.length > 0 && process.argv.includes('--force')) {
                await this.forceStop(results.failed);
            }
            
            // Final status check
            const finalStatus = this.botManager.getStatus();
            
            console.log('\n' + '='.repeat(60));
            console.log('📊 Final Status:');
            console.log(`   Running Bots: ${finalStatus.runningBots}`);
            
            if (finalStatus.runningBots === 0) {
                console.log('✅ All bots stopped successfully!');
            } else {
                console.log(`⚠️  ${finalStatus.runningBots} bots still running:`);
                finalStatus.runningBots.forEach(clientId => {
                    console.log(`   • ${clientId}`);
                });
                
                if (!process.argv.includes('--force')) {
                    console.log('\n💡 Use --force flag to forcefully stop remaining bots');
                }
            }
            console.log('='.repeat(60));
            
        } catch (error) {
            console.error('❌ Failed to stop bots:', error);
            process.exit(1);
        }
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Multi-Tenant Bot Manager - Stop All Bots

Usage: node scripts/stop-all.js [options]

Options:
  --force           Force stop all bots immediately without confirmation
  --help, -h        Show this help message

Examples:
  node scripts/stop-all.js
  node scripts/stop-all.js --force
    `);
    process.exit(0);
}

// Run the script
if (require.main === module) {
    const script = new StopAllScript();
    script.run().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}

module.exports = StopAllScript;
