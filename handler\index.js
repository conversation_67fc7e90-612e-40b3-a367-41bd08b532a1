// index.js

const { glob } = require("glob");
const { promisify } = require("util");
const fs = require('fs');
const path = require('path');
const globPromise = promisify(glob);
const Data = require("pro.db");



module.exports = async (client) => {
    // Load commands using fs.readdirSync instead of glob
    const commandsPath = path.join(process.cwd(), 'Commands');
    const commandFolders = fs.readdirSync(commandsPath);

    for (const folder of commandFolders) {
        const folderPath = path.join(commandsPath, folder);
        if (!fs.statSync(folderPath).isDirectory()) continue;

        const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            try {
                const filePath = path.join(folderPath, file);
                const command = require(filePath);

                const isEnabled = Data.get(`command_enabled_${command.name}`);
                if (isEnabled === false) continue;

                if (command.name) {
                    const properties = { directory: folder, ...command };
                    client.commands.set(command.name, properties);

                    // تحقق من حالة التشغيل للـ aliases
                    if (command.aliases && Array.isArray(command.aliases)) {
                        command.aliases.forEach(alias => {
                            const aliasIsEnabled = Data.get(`command_enabled_${alias}`);
                            if (aliasIsEnabled === false) return;
                            client.commands.set(alias, properties);
                        });
                    }
                }
            } catch (error) {
                console.error(`Error loading command from ${file}:`, error);
            }
        }
    }


    
    const eventFiles = await globPromise(`${process.cwd()}/events/**/*.js`);
    eventFiles.map((value) => require(value));

    fs.readdirSync('./Extras/Guild/').filter(file => file.endsWith('.js')).forEach(file => {
        require(`../Extras/Guild/${file}`);
    });
};
