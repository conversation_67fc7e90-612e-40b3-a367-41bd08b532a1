// Multi-tenant <PERSON><PERSON> Handler
// Enhanced to support multiple bot instances with client isolation

const { glob } = require("glob");
const { promisify } = require("util");
const fs = require('fs');
const path = require('path');
const globPromise = promisify(glob);
const Data = require("pro.db");

module.exports = async (client) => {
    // Get client ID for data isolation
    const clientId = client.clientId || 'default';

    // Create client-specific database prefix
    const getClientKey = (key) => `client_${clientId}_${key}`;

    // Attach client-specific database methods to client
    client.db = {
        get: (key) => Data.get(getClientKey(key)),
        set: (key, value) => Data.set(getClientKey(key), value),
        delete: (key) => Data.delete(getClientKey(key)),
        has: (key) => Data.has(getClientKey(key)),
        fetch: (key) => Data.fetch(getClient<PERSON>ey(key)),
        add: (key, value) => Data.add(getClientKey(key), value),
        subtract: (key, value) => Data.subtract(getClientKey(key), value)
    };

    // Load commands using fs.readdirSync instead of glob
    const commandsPath = path.join(process.cwd(), 'Commands');
    const commandFolders = fs.readdirSync(commandsPath);

    for (const folder of commandFolders) {
        const folderPath = path.join(commandsPath, folder);
        if (!fs.statSync(folderPath).isDirectory()) continue;

        const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            try {
                const filePath = path.join(folderPath, file);
                const command = require(filePath);

                // Use client-specific database for command state
                const isEnabled = client.db.get(`command_enabled_${command.name}`);
                if (isEnabled === false) continue;

                if (command.name) {
                    const properties = { directory: folder, ...command };
                    client.commands.set(command.name, properties);

                    // Check aliases with client-specific database
                    if (command.aliases && Array.isArray(command.aliases)) {
                        command.aliases.forEach(alias => {
                            const aliasIsEnabled = client.db.get(`command_enabled_${alias}`);
                            if (aliasIsEnabled === false) return;
                            client.commands.set(alias, properties);
                        });
                    }
                }
            } catch (error) {
                console.error(`Error loading command from ${file} for client ${clientId}:`, error);
            }
        }
    }

    // Load events
    const eventFiles = await globPromise(`${process.cwd()}/events/**/*.js`);
    eventFiles.map((value) => require(value));

    // Load guild extras if directory exists
    const extrasPath = './Extras/Guild/';
    if (fs.existsSync(extrasPath)) {
        fs.readdirSync(extrasPath).filter(file => file.endsWith('.js')).forEach(file => {
            require(`../Extras/Guild/${file}`);
        });
    }

    console.log(`✅ Handler loaded for client: ${clientId}`);
};
