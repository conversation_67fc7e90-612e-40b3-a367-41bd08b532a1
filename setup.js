#!/usr/bin/env node

// Multi-Tenant Bot System Setup Script
// Automated setup and configuration wizard

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class SetupWizard {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * Display welcome banner
     */
    displayBanner() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                        Setup Wizard                         ║
╚══════════════════════════════════════════════════════════════╝

Welcome to the Multi-Tenant Discord Bot Management System!
This wizard will help you set up your bot management system.
        `);
    }

    /**
     * Ask a question and return the answer
     */
    async ask(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * Create necessary directories
     */
    createDirectories() {
        const directories = ['config', 'manager', 'scripts'];
        
        console.log('\n📁 Creating directories...');
        
        directories.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`   ✅ Created: ${dir}/`);
            } else {
                console.log(`   ✓ Exists: ${dir}/`);
            }
        });
    }

    /**
     * Copy example configuration files
     */
    copyExampleConfigs() {
        console.log('\n📄 Setting up configuration files...');
        
        const configs = [
            { src: 'config/clients.example.json', dest: 'config/clients.json' },
            { src: 'config/subscriptions.example.json', dest: 'config/subscriptions.json' }
        ];

        configs.forEach(({ src, dest }) => {
            if (!fs.existsSync(dest)) {
                if (fs.existsSync(src)) {
                    fs.copyFileSync(src, dest);
                    console.log(`   ✅ Created: ${dest}`);
                } else {
                    console.log(`   ⚠️  Example file not found: ${src}`);
                    this.createDefaultConfig(dest);
                }
            } else {
                console.log(`   ✓ Exists: ${dest}`);
            }
        });
    }

    /**
     * Create default configuration if example doesn't exist
     */
    createDefaultConfig(configPath) {
        if (configPath.includes('clients.json')) {
            const defaultConfig = {
                clients: {},
                settings: {
                    maxClients: 100,
                    defaultPrefix: '#',
                    encryptTokens: true
                }
            };
            fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 4));
            console.log(`   ✅ Created default: ${configPath}`);
        } else if (configPath.includes('subscriptions.json')) {
            const defaultConfig = {
                clients: {},
                plans: {
                    basic: {
                        name: "Basic Plan",
                        maxGuilds: 1,
                        features: ["basic_commands", "moderation"],
                        price: 9.99
                    },
                    premium: {
                        name: "Premium Plan",
                        maxGuilds: 5,
                        features: ["basic_commands", "moderation", "advanced_features", "custom_commands"],
                        price: 19.99
                    },
                    enterprise: {
                        name: "Enterprise Plan",
                        maxGuilds: -1,
                        features: ["all_features"],
                        price: 49.99
                    }
                }
            };
            fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 4));
            console.log(`   ✅ Created default: ${configPath}`);
        }
    }

    /**
     * Validate Discord bot token format
     */
    validateToken(token) {
        // Updated Discord token format validation
        // New format: 18+ chars . 6+ chars . 25+ chars (more flexible)
        const tokenRegex = /^[A-Za-z0-9_-]{18,}\.[A-Za-z0-9_-]{6,}\.[A-Za-z0-9_-]{25,}$/;

        // Also check for basic structure (3 parts separated by dots)
        const parts = token.split('.');
        if (parts.length !== 3) return false;

        // Check minimum lengths for each part
        if (parts[0].length < 18 || parts[1].length < 6 || parts[2].length < 25) {
            return false;
        }

        return tokenRegex.test(token);
    }

    /**
     * Add first client configuration
     */
    async addFirstClient() {
        console.log('\n🤖 Let\'s add your first bot client!');
        console.log('You\'ll need a Discord bot token. Get one from: https://discord.com/developers/applications');
        
        const addClient = await this.ask('\nWould you like to add a client now? (y/n): ');
        if (addClient.toLowerCase() !== 'y' && addClient.toLowerCase() !== 'yes') {
            console.log('   ⏭️  Skipping client setup. You can add clients later with:');
            console.log('      npm run manage-client add "YOUR_TOKEN" client_id');
            return;
        }

        let token;
        while (true) {
            token = await this.ask('\nEnter your Discord bot token: ');
            if (!token) {
                console.log('   ❌ Token cannot be empty');
                continue;
            }
            if (!this.validateToken(token)) {
                console.log('   ❌ Invalid token format. Discord tokens should be in format: XXXXXXXXXX.XXXXXX.XXXXXXXXXXXXXXXXXXXXXXXXXXX');
                const continueAnyway = await this.ask('   Continue anyway? (y/n): ');
                if (continueAnyway.toLowerCase() === 'y' || continueAnyway.toLowerCase() === 'yes') {
                    break;
                }
                continue;
            }
            break;
        }

        const clientId = await this.ask('Enter a client ID (or press Enter for auto-generated): ') || 
                        `client_${Math.random().toString(36).substr(2, 8)}`;
        
        const clientName = await this.ask('Enter a client name (or press Enter for default): ') || 
                          `Client ${clientId}`;
        
        const prefix = await this.ask('Enter bot prefix (or press Enter for #): ') || '#';
        
        console.log('\nAvailable subscription plans:');
        console.log('  1. basic - 1 guild, basic features ($9.99)');
        console.log('  2. premium - 5 guilds, advanced features ($19.99)');
        console.log('  3. enterprise - unlimited guilds, all features ($49.99)');
        
        let plan;
        while (true) {
            const planChoice = await this.ask('Choose plan (1-3 or plan name): ');
            if (planChoice === '1' || planChoice.toLowerCase() === 'basic') {
                plan = 'basic';
                break;
            } else if (planChoice === '2' || planChoice.toLowerCase() === 'premium') {
                plan = 'premium';
                break;
            } else if (planChoice === '3' || planChoice.toLowerCase() === 'enterprise') {
                plan = 'enterprise';
                break;
            } else {
                console.log('   ❌ Invalid choice. Please enter 1, 2, 3, or the plan name.');
            }
        }

        // Add client using the management system
        try {
            const TokenManager = require('./manager/TokenManager');
            const SubscriptionManager = require('./manager/SubscriptionManager');
            
            const tokenManager = new TokenManager();
            const subscriptionManager = new SubscriptionManager();

            // Add client configuration
            const clientConfig = {
                token,
                name: clientName,
                prefix,
                owners: [],
                features: []
            };

            const tokenSuccess = tokenManager.addClient(clientId, clientConfig);
            if (!tokenSuccess) {
                console.log('   ❌ Failed to add client configuration');
                return;
            }

            // Create subscription
            const subscriptionData = {
                plan,
                status: 'active'
            };

            const subscriptionSuccess = subscriptionManager.createSubscription(clientId, subscriptionData);
            if (!subscriptionSuccess) {
                tokenManager.removeClient(clientId);
                console.log('   ❌ Failed to create subscription');
                return;
            }

            console.log('\n✅ Client added successfully!');
            console.log(`   Client ID: ${clientId}`);
            console.log(`   Name: ${clientName}`);
            console.log(`   Plan: ${plan}`);
            console.log(`   Prefix: ${prefix}`);

        } catch (error) {
            console.log('   ❌ Error adding client:', error.message);
            console.log('   You can add clients manually later with:');
            console.log(`      npm run manage-client add "${token}" ${clientId} --name "${clientName}" --plan ${plan}`);
        }
    }

    /**
     * Test system setup
     */
    async testSetup() {
        console.log('\n🧪 Testing system setup...');
        
        try {
            // Test manager imports
            const BotManager = require('./manager/BotManager');
            const SubscriptionManager = require('./manager/SubscriptionManager');
            const TokenManager = require('./manager/TokenManager');
            
            console.log('   ✅ Manager modules loaded successfully');

            // Test configuration files
            const tokenManager = new TokenManager();
            const subscriptionManager = new SubscriptionManager();
            
            const stats = tokenManager.getStatistics();
            const subStats = subscriptionManager.getStatistics();
            
            console.log('   ✅ Configuration files loaded successfully');
            console.log(`   📊 System ready: ${stats.totalClients} clients, ${subStats.active} active subscriptions`);

        } catch (error) {
            console.log('   ❌ Setup test failed:', error.message);
            console.log('   Please check the installation and try again.');
            return false;
        }
        
        return true;
    }

    /**
     * Display next steps
     */
    displayNextSteps() {
        console.log('\n🎉 Setup completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Start the management server:');
        console.log('      node multi-tenant-bot.js');
        console.log('');
        console.log('   2. Or start all bots directly:');
        console.log('      npm run start-all');
        console.log('');
        console.log('   3. Manage clients:');
        console.log('      npm run list-clients');
        console.log('      npm run client-status');
        console.log('');
        console.log('   4. Add more clients:');
        console.log('      npm run manage-client add "TOKEN" client_id');
        console.log('');
        console.log('   5. View documentation:');
        console.log('      cat README.md');
        console.log('');
        console.log('🌐 Management API will be available at: http://localhost:3000');
        console.log('📚 Full documentation: README.md');
    }

    /**
     * Main setup process
     */
    async run() {
        try {
            this.displayBanner();
            
            // Create directories
            this.createDirectories();
            
            // Copy configuration files
            this.copyExampleConfigs();
            
            // Add first client
            await this.addFirstClient();
            
            // Test setup
            const testPassed = await this.testSetup();
            
            if (testPassed) {
                this.displayNextSteps();
            } else {
                console.log('\n❌ Setup incomplete. Please check the errors above and try again.');
            }

        } catch (error) {
            console.error('\n❌ Setup failed:', error);
        } finally {
            this.rl.close();
        }
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Multi-Tenant Bot Manager Setup

Usage: node setup.js [options]

Options:
  --help, -h        Show this help message

This script will:
  1. Create necessary directories
  2. Set up configuration files
  3. Guide you through adding your first client
  4. Test the system setup
    `);
    process.exit(0);
}

// Run the setup wizard
if (require.main === module) {
    const wizard = new SetupWizard();
    wizard.run().catch(error => {
        console.error('❌ Setup wizard failed:', error);
        process.exit(1);
    });
}

module.exports = SetupWizard;
