// Multi-Tenant Bot System Entry Point
// Main file for running the multi-tenant Discord bot management system

const BotManager = require('./manager/BotManager');
const express = require('express');
const path = require('path');

class MultiTenantBotSystem {
    constructor() {
        this.botManager = new BotManager();
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.setupExpress();
        this.setupSignalHandlers();
    }

    /**
     * Setup Express server for health checks and management
     */
    setupExpress() {
        this.app.use(express.json());
        
        // Health check endpoint
        this.app.get('/', (req, res) => {
            const status = this.botManager.getStatus();
            res.json({
                message: 'Multi-Tenant Bot Manager',
                status: 'running',
                bots: {
                    total: status.totalActiveClients,
                    running: status.runningBots,
                    active: status.activeClients.length
                },
                timestamp: new Date().toISOString()
            });
        });

        // Status endpoint
        this.app.get('/status', (req, res) => {
            const status = this.botManager.getStatus();
            const subStats = this.botManager.subscriptionManager.getStatistics();
            const tokenStats = this.botManager.tokenManager.getStatistics();

            res.json({
                system: {
                    totalClients: tokenStats.totalClients,
                    runningBots: status.runningBots,
                    activeSubscriptions: subStats.active,
                    availableSlots: tokenStats.availableSlots
                },
                bots: {
                    running: status.runningBots,
                    notRunning: status.notRunning,
                    runningButInactive: status.runningButInactive
                },
                subscriptions: subStats,
                timestamp: new Date().toISOString()
            });
        });

        // Start specific bot endpoint
        this.app.post('/bot/:clientId/start', async (req, res) => {
            try {
                const { clientId } = req.params;
                const success = await this.botManager.startBot(clientId);
                
                res.json({
                    success,
                    message: success ? `Bot started for ${clientId}` : `Failed to start bot for ${clientId}`,
                    clientId,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Stop specific bot endpoint
        this.app.post('/bot/:clientId/stop', async (req, res) => {
            try {
                const { clientId } = req.params;
                const success = await this.botManager.stopBot(clientId);
                
                res.json({
                    success,
                    message: success ? `Bot stopped for ${clientId}` : `Failed to stop bot for ${clientId}`,
                    clientId,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Start all bots endpoint
        this.app.post('/bots/start-all', async (req, res) => {
            try {
                const results = await this.botManager.startAllBots();
                
                res.json({
                    success: results.failed.length === 0,
                    results,
                    message: `Started ${results.success.length} bots, ${results.failed.length} failed`,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Stop all bots endpoint
        this.app.post('/bots/stop-all', async (req, res) => {
            try {
                const results = await this.botManager.stopAllBots();
                
                res.json({
                    success: results.failed.length === 0,
                    results,
                    message: `Stopped ${results.success.length} bots, ${results.failed.length} failed`,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Get bot info endpoint
        this.app.get('/bot/:clientId', (req, res) => {
            try {
                const { clientId } = req.params;
                const config = this.botManager.tokenManager.getClientConfig(clientId);
                const subscription = this.botManager.subscriptionManager.getClientSubscription(clientId);
                const isRunning = this.botManager.isBotRunning(clientId);

                if (!config) {
                    return res.status(404).json({
                        success: false,
                        error: 'Client not found',
                        timestamp: new Date().toISOString()
                    });
                }

                res.json({
                    clientId,
                    name: config.name,
                    status: isRunning ? 'running' : 'stopped',
                    subscription: {
                        status: subscription?.status,
                        plan: subscription?.plan,
                        expiresAt: subscription?.expiresAt
                    },
                    config: {
                        prefix: config.prefix,
                        createdAt: config.createdAt,
                        lastActive: config.lastActive
                    },
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Error handling middleware
        this.app.use((error, req, res, next) => {
            console.error('Express error:', error);
            res.status(500).json({
                success: false,
                error: 'Internal server error',
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * Setup signal handlers for graceful shutdown
     */
    setupSignalHandlers() {
        process.on('SIGINT', this.handleShutdown.bind(this));
        process.on('SIGTERM', this.handleShutdown.bind(this));
        process.on('uncaughtException', this.handleError.bind(this));
        process.on('unhandledRejection', this.handleError.bind(this));
    }

    /**
     * Handle graceful shutdown
     */
    async handleShutdown(signal) {
        console.log(`\n🔄 Received ${signal}. Initiating graceful shutdown...`);
        try {
            // Stop all bots
            await this.botManager.shutdown();
            
            // Close Express server
            if (this.server) {
                this.server.close(() => {
                    console.log('✅ Express server closed');
                });
            }
            
            console.log('✅ Graceful shutdown completed');
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }

    /**
     * Handle uncaught errors
     */
    handleError(error) {
        console.error('❌ Uncaught error:', error);
        // Don't exit immediately, let the system handle cleanup
    }

    /**
     * Start the multi-tenant bot system
     */
    async start() {
        try {
            console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                      System Starting...                     ║
╚══════════════════════════════════════════════════════════════╝
            `);

            // Start Express server
            this.server = this.app.listen(this.port, () => {
                console.log(`🌐 Management server started on port ${this.port}`);
                console.log(`   Health check: http://localhost:${this.port}/`);
                console.log(`   Status API: http://localhost:${this.port}/status`);
            });

            // Display system information
            const tokenStats = this.botManager.tokenManager.getStatistics();
            const subStats = this.botManager.subscriptionManager.getStatistics();

            console.log('\n📊 System Information:');
            console.log(`   Total Clients: ${tokenStats.totalClients}`);
            console.log(`   Active Subscriptions: ${subStats.active}`);
            console.log(`   Available Slots: ${tokenStats.availableSlots}`);

            // Auto-start all active bots if requested
            if (process.argv.includes('--auto-start')) {
                console.log('\n🚀 Auto-starting all active bots...');
                await this.botManager.startAllBots();
            } else {
                console.log('\n💡 Use --auto-start flag to automatically start all bots');
                console.log('   Or use: npm run start-all');
            }

            console.log('\n✅ Multi-tenant bot system is ready!');
            console.log('   Press Ctrl+C to stop all bots and shutdown');

        } catch (error) {
            console.error('❌ Failed to start multi-tenant bot system:', error);
            process.exit(1);
        }
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Multi-Tenant Bot Management System

Usage: node multi-tenant-bot.js [options]

Options:
  --auto-start      Automatically start all active bots on system startup
  --port <port>     Set the management server port (default: 3000)
  --help, -h        Show this help message

Examples:
  node multi-tenant-bot.js
  node multi-tenant-bot.js --auto-start
  node multi-tenant-bot.js --port 8080 --auto-start

Management Commands:
  npm run start-all         Start all active bots
  npm run stop-all          Stop all running bots
  npm run manage-client     Manage individual clients
  npm run list-clients      List all clients
    `);
    process.exit(0);
}

// Set port from command line if provided
const portIndex = args.indexOf('--port');
if (portIndex !== -1 && portIndex + 1 < args.length) {
    process.env.PORT = args[portIndex + 1];
}

// Run the system
if (require.main === module) {
    const system = new MultiTenantBotSystem();
    system.start().catch(error => {
        console.error('❌ System startup failed:', error);
        process.exit(1);
    });
}

module.exports = MultiTenantBotSystem;
