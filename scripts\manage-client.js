#!/usr/bin/env node

// Client Management Script
// Handles adding, removing, and managing individual clients

const BotManager = require('../manager/BotManager');
const SubscriptionManager = require('../manager/SubscriptionManager');
const TokenManager = require('../manager/TokenManager');

class ClientManagerScript {
    constructor() {
        this.botManager = new BotManager();
        this.subscriptionManager = new SubscriptionManager();
        this.tokenManager = new TokenManager();
    }

    /**
     * Display help information
     */
    displayHelp() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                      Client Management                       ║
╚══════════════════════════════════════════════════════════════╝

Usage: node scripts/manage-client.js <command> [options]

Commands:
  add <token> [clientId]           Add a new client
  remove <clientId>                Remove a client
  start <clientId>                 Start a specific client's bot
  stop <clientId>                  Stop a specific client's bot
  restart <clientId>               Restart a specific client's bot
  status [clientId]                Show status (all clients or specific)
  list                             List all clients
  subscription <clientId> <action> Manage subscription
  update <clientId> <key> <value>  Update client configuration

Subscription Actions:
  activate                         Activate subscription
  suspend [reason]                 Suspend subscription
  delete                           Delete subscription
  plan <planName>                  Change subscription plan

Options:
  --name <name>                    Client name (for add command)
  --prefix <prefix>                Bot prefix (for add command)
  --plan <plan>                    Subscription plan (basic/premium/enterprise)
  --force                          Force action without confirmation
  --verbose                        Enable verbose output

Examples:
  node scripts/manage-client.js add "BOT_TOKEN_HERE" my_client_1
  node scripts/manage-client.js remove my_client_1 --force
  node scripts/manage-client.js start my_client_1
  node scripts/manage-client.js status
  node scripts/manage-client.js subscription my_client_1 activate
  node scripts/manage-client.js subscription my_client_1 plan premium
        `);
    }

    /**
     * Add a new client
     */
    async addClient(token, clientId, options = {}) {
        try {
            // Generate client ID if not provided
            if (!clientId) {
                clientId = this.tokenManager.generateClientId();
            }

            // Validate token
            if (!this.tokenManager.validateToken(token)) {
                console.error('❌ Invalid Discord bot token format');
                return false;
            }

            // Check if client already exists
            if (this.tokenManager.clientExists(clientId)) {
                console.error(`❌ Client ${clientId} already exists`);
                return false;
            }

            // Prepare client configuration
            const clientConfig = {
                token,
                name: options.name || `Client ${clientId}`,
                prefix: options.prefix || '#',
                owners: options.owners || [],
                features: ['basic_commands', 'moderation']
            };

            // Add client to token manager
            const tokenSuccess = this.tokenManager.addClient(clientId, clientConfig);
            if (!tokenSuccess) {
                return false;
            }

            // Create subscription
            const subscriptionData = {
                plan: options.plan || 'basic',
                status: 'active'
            };

            const subscriptionSuccess = this.subscriptionManager.createSubscription(clientId, subscriptionData);
            if (!subscriptionSuccess) {
                // Rollback token manager changes
                this.tokenManager.removeClient(clientId);
                return false;
            }

            console.log(`✅ Client ${clientId} added successfully!`);
            console.log(`   Name: ${clientConfig.name}`);
            console.log(`   Plan: ${subscriptionData.plan}`);
            console.log(`   Prefix: ${clientConfig.prefix}`);
            
            return true;

        } catch (error) {
            console.error('❌ Error adding client:', error);
            return false;
        }
    }

    /**
     * Remove a client
     */
    async removeClient(clientId, force = false) {
        try {
            if (!this.tokenManager.clientExists(clientId)) {
                console.error(`❌ Client ${clientId} not found`);
                return false;
            }

            // Confirm removal if not forced
            if (!force) {
                console.log(`⚠️  This will permanently remove client ${clientId} and all associated data.`);
                console.log('   Use --force flag to skip this confirmation.');
                return false;
            }

            // Stop bot if running
            if (this.botManager.isBotRunning(clientId)) {
                console.log(`🛑 Stopping bot for client ${clientId}...`);
                await this.botManager.stopBot(clientId);
            }

            // Remove from subscription manager
            this.subscriptionManager.deleteClient(clientId);

            // Remove from token manager
            this.tokenManager.removeClient(clientId);

            console.log(`✅ Client ${clientId} removed successfully!`);
            return true;

        } catch (error) {
            console.error('❌ Error removing client:', error);
            return false;
        }
    }

    /**
     * Start a specific client's bot
     */
    async startClient(clientId) {
        try {
            if (!this.tokenManager.clientExists(clientId)) {
                console.error(`❌ Client ${clientId} not found`);
                return false;
            }

            console.log(`🚀 Starting bot for client ${clientId}...`);
            const success = await this.botManager.startBot(clientId);
            
            if (success) {
                console.log(`✅ Bot started successfully for client ${clientId}`);
            } else {
                console.log(`❌ Failed to start bot for client ${clientId}`);
            }
            
            return success;

        } catch (error) {
            console.error('❌ Error starting client:', error);
            return false;
        }
    }

    /**
     * Stop a specific client's bot
     */
    async stopClient(clientId) {
        try {
            if (!this.botManager.isBotRunning(clientId)) {
                console.log(`⚠️  Bot for client ${clientId} is not running`);
                return true;
            }

            console.log(`🛑 Stopping bot for client ${clientId}...`);
            const success = await this.botManager.stopBot(clientId);
            
            if (success) {
                console.log(`✅ Bot stopped successfully for client ${clientId}`);
            } else {
                console.log(`❌ Failed to stop bot for client ${clientId}`);
            }
            
            return success;

        } catch (error) {
            console.error('❌ Error stopping client:', error);
            return false;
        }
    }

    /**
     * Restart a specific client's bot
     */
    async restartClient(clientId) {
        try {
            console.log(`🔄 Restarting bot for client ${clientId}...`);
            const success = await this.botManager.restartBot(clientId);
            
            if (success) {
                console.log(`✅ Bot restarted successfully for client ${clientId}`);
            } else {
                console.log(`❌ Failed to restart bot for client ${clientId}`);
            }
            
            return success;

        } catch (error) {
            console.error('❌ Error restarting client:', error);
            return false;
        }
    }

    /**
     * Show status information
     */
    showStatus(clientId = null) {
        if (clientId) {
            // Show specific client status
            const config = this.tokenManager.getClientConfig(clientId);
            const subscription = this.subscriptionManager.getClientSubscription(clientId);
            const isRunning = this.botManager.isBotRunning(clientId);

            if (!config) {
                console.error(`❌ Client ${clientId} not found`);
                return;
            }

            console.log(`\n📊 Status for Client: ${clientId}`);
            console.log(`   Name: ${config.name}`);
            console.log(`   Status: ${isRunning ? '🟢 Running' : '🔴 Stopped'}`);
            console.log(`   Subscription: ${subscription?.status || 'Unknown'}`);
            console.log(`   Plan: ${subscription?.plan || 'Unknown'}`);
            console.log(`   Prefix: ${config.prefix}`);
            console.log(`   Created: ${config.createdAt}`);

        } else {
            // Show overall status
            const botStatus = this.botManager.getStatus();
            const subStats = this.subscriptionManager.getStatistics();
            const tokenStats = this.tokenManager.getStatistics();

            console.log('\n📊 System Status:');
            console.log(`   Total Clients: ${tokenStats.totalClients}`);
            console.log(`   Running Bots: ${botStatus.runningBots}`);
            console.log(`   Active Subscriptions: ${subStats.active}`);
            console.log(`   Suspended: ${subStats.suspended}`);
            console.log(`   Expired: ${subStats.expired}`);
            console.log(`   Available Slots: ${tokenStats.availableSlots}`);
        }
    }

    /**
     * List all clients
     */
    listClients() {
        const clients = this.tokenManager.getClientsInfo();
        
        console.log('\n📋 All Clients:');
        
        if (Object.keys(clients.clients).length === 0) {
            console.log('   No clients found');
            return;
        }

        Object.entries(clients.clients).forEach(([clientId, config]) => {
            const subscription = this.subscriptionManager.getClientSubscription(clientId);
            const isRunning = this.botManager.isBotRunning(clientId);
            
            console.log(`\n   • ${clientId}`);
            console.log(`     Name: ${config.name}`);
            console.log(`     Status: ${isRunning ? '🟢 Running' : '🔴 Stopped'}`);
            console.log(`     Subscription: ${subscription?.status || 'Unknown'} (${subscription?.plan || 'Unknown'})`);
            console.log(`     Created: ${config.createdAt}`);
        });
    }

    /**
     * Manage subscription
     */
    async manageSubscription(clientId, action, value = null) {
        try {
            if (!this.tokenManager.clientExists(clientId)) {
                console.error(`❌ Client ${clientId} not found`);
                return false;
            }

            switch (action) {
                case 'activate':
                    return this.subscriptionManager.reactivateClient(clientId);
                
                case 'suspend':
                    const reason = value || 'Administrative action';
                    return this.subscriptionManager.suspendClient(clientId, reason);
                
                case 'delete':
                    return this.subscriptionManager.deleteClient(clientId);
                
                case 'plan':
                    if (!value) {
                        console.error('❌ Plan name required');
                        return false;
                    }
                    return this.subscriptionManager.updateSubscription(clientId, { plan: value });
                
                default:
                    console.error(`❌ Unknown subscription action: ${action}`);
                    return false;
            }

        } catch (error) {
            console.error('❌ Error managing subscription:', error);
            return false;
        }
    }

    /**
     * Main execution function
     */
    async run() {
        const args = process.argv.slice(2);
        
        if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
            this.displayHelp();
            return;
        }

        const command = args[0];
        const options = {
            force: args.includes('--force'),
            verbose: args.includes('--verbose'),
            name: this.getArgValue(args, '--name'),
            prefix: this.getArgValue(args, '--prefix'),
            plan: this.getArgValue(args, '--plan')
        };

        try {
            switch (command) {
                case 'add':
                    if (args.length < 2) {
                        console.error('❌ Token required for add command');
                        return;
                    }
                    await this.addClient(args[1], args[2], options);
                    break;

                case 'remove':
                    if (args.length < 2) {
                        console.error('❌ Client ID required for remove command');
                        return;
                    }
                    await this.removeClient(args[1], options.force);
                    break;

                case 'start':
                    if (args.length < 2) {
                        console.error('❌ Client ID required for start command');
                        return;
                    }
                    await this.startClient(args[1]);
                    break;

                case 'stop':
                    if (args.length < 2) {
                        console.error('❌ Client ID required for stop command');
                        return;
                    }
                    await this.stopClient(args[1]);
                    break;

                case 'restart':
                    if (args.length < 2) {
                        console.error('❌ Client ID required for restart command');
                        return;
                    }
                    await this.restartClient(args[1]);
                    break;

                case 'status':
                    this.showStatus(args[1]);
                    break;

                case 'list':
                    this.listClients();
                    break;

                case 'subscription':
                    if (args.length < 3) {
                        console.error('❌ Client ID and action required for subscription command');
                        return;
                    }
                    await this.manageSubscription(args[1], args[2], args[3]);
                    break;

                default:
                    console.error(`❌ Unknown command: ${command}`);
                    this.displayHelp();
            }

        } catch (error) {
            console.error('❌ Command execution failed:', error);
            process.exit(1);
        }
    }

    /**
     * Get argument value by flag
     */
    getArgValue(args, flag) {
        const index = args.indexOf(flag);
        return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
    }
}

// Run the script
if (require.main === module) {
    const script = new ClientManagerScript();
    script.run().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}

module.exports = ClientManagerScript;
