#!/usr/bin/env node

// Start All Bots Script
// Starts all active bot instances with proper error handling and logging

const BotManager = require('../manager/BotManager');
const path = require('path');

class StartAllScript {
    constructor() {
        this.botManager = new BotManager();
        this.setupSignalHandlers();
    }

    /**
     * Setup signal handlers for graceful shutdown
     */
    setupSignalHandlers() {
        process.on('SIGINT', this.handleShutdown.bind(this));
        process.on('SIGTERM', this.handleShutdown.bind(this));
        process.on('uncaughtException', this.handleError.bind(this));
        process.on('unhandledRejection', this.handleError.bind(this));
    }

    /**
     * Handle graceful shutdown
     */
    async handleShutdown(signal) {
        console.log(`\n🔄 Received ${signal}. Initiating graceful shutdown...`);
        try {
            await this.botManager.shutdown();
            console.log('✅ All bots shut down successfully');
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }

    /**
     * Handle uncaught errors
     */
    handleError(error) {
        console.error('❌ Uncaught error:', error);
        // Don't exit immediately, let the bot manager handle cleanup
    }

    /**
     * Display startup banner
     */
    displayBanner() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Multi-Tenant Bot Manager                  ║
║                        Starting All Bots                    ║
╚══════════════════════════════════════════════════════════════╝
        `);
    }

    /**
     * Display status information
     */
    async displayStatus() {
        const status = this.botManager.getStatus();
        const stats = this.botManager.subscriptionManager.getStatistics();
        
        console.log('\n📊 System Status:');
        console.log(`   Total Active Clients: ${status.totalActiveClients}`);
        console.log(`   Currently Running: ${status.runningBots}`);
        console.log(`   Subscription Stats: ${stats.active} active, ${stats.suspended} suspended, ${stats.expired} expired`);
        
        if (status.notRunning.length > 0) {
            console.log(`   ⚠️  Not Running: ${status.notRunning.join(', ')}`);
        }
        
        if (status.runningButInactive.length > 0) {
            console.log(`   ⚠️  Running but Inactive: ${status.runningButInactive.join(', ')}`);
        }
    }

    /**
     * Start all bots with detailed logging
     */
    async startAllBots() {
        try {
            console.log('\n🚀 Starting bot instances...\n');
            
            const results = await this.botManager.startAllBots();
            
            console.log('\n📋 Startup Results:');
            console.log(`   ✅ Successfully Started: ${results.success.length}`);
            
            if (results.success.length > 0) {
                results.success.forEach(clientId => {
                    console.log(`      • ${clientId}`);
                });
            }
            
            if (results.failed.length > 0) {
                console.log(`   ❌ Failed to Start: ${results.failed.length}`);
                results.failed.forEach(clientId => {
                    console.log(`      • ${clientId}`);
                });
            }
            
            return results;
            
        } catch (error) {
            console.error('❌ Error starting bots:', error);
            throw error;
        }
    }

    /**
     * Monitor bot health
     */
    startHealthMonitoring() {
        console.log('\n🔍 Starting health monitoring...');
        
        setInterval(() => {
            const status = this.botManager.getStatus();
            const timestamp = new Date().toLocaleTimeString();
            
            console.log(`[${timestamp}] Health Check: ${status.runningBots}/${status.totalActiveClients} bots running`);
            
            // Check for bots that should be running but aren't
            if (status.notRunning.length > 0) {
                console.log(`⚠️  Attempting to restart failed bots: ${status.notRunning.join(', ')}`);
                status.notRunning.forEach(async (clientId) => {
                    await this.botManager.startBot(clientId);
                });
            }
            
        }, 60000); // Check every minute
    }

    /**
     * Main execution function
     */
    async run() {
        try {
            this.displayBanner();
            
            // Display initial status
            await this.displayStatus();
            
            // Start all bots
            const results = await this.startAllBots();
            
            if (results.success.length === 0) {
                console.log('\n⚠️  No bots were started. Check your configuration and subscriptions.');
                process.exit(1);
            }
            
            // Display final status
            console.log('\n' + '='.repeat(60));
            await this.displayStatus();
            console.log('='.repeat(60));
            
            // Start health monitoring
            this.startHealthMonitoring();
            
            console.log('\n✅ All systems operational! Press Ctrl+C to stop all bots.');
            console.log('📊 Health monitoring active - checking every 60 seconds');
            
            // Keep the process alive
            process.stdin.resume();
            
        } catch (error) {
            console.error('❌ Failed to start bot manager:', error);
            process.exit(1);
        }
    }
}

// Handle command line arguments
const args = process.argv.slice(2);
const options = {
    verbose: args.includes('--verbose') || args.includes('-v'),
    monitor: !args.includes('--no-monitor'),
    force: args.includes('--force')
};

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Multi-Tenant Bot Manager - Start All Bots

Usage: node scripts/start-all.js [options]

Options:
  --verbose, -v     Enable verbose logging
  --no-monitor      Disable health monitoring
  --force           Force start even if some bots fail
  --help, -h        Show this help message

Examples:
  node scripts/start-all.js
  node scripts/start-all.js --verbose
  node scripts/start-all.js --no-monitor
    `);
    process.exit(0);
}

// Run the script
if (require.main === module) {
    const script = new StartAllScript();
    script.run().catch(error => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
    });
}

module.exports = StartAllScript;
