# Multi-Tenant Discord Bot Management System

A comprehensive system for managing multiple Discord bot instances with subscription management, centralized control, and isolated client environments.

## 🚀 Features

### Multi-Tenant Architecture
- **Up to 100 bot instances** - Support for managing multiple Discord bots simultaneously
- **Token-based isolation** - Each client has their own dedicated bot instance
- **Client-specific data** - Isolated database storage for each client
- **Independent configurations** - Custom prefixes, settings, and features per client

### Subscription Management
- **Flexible subscription plans** - Basic, Premium, and Enterprise tiers
- **Access control** - Feature-based permissions and guild limits
- **Payment tracking** - Subscription status and billing management
- **Automatic enforcement** - Inactive subscriptions prevent bot startup

### Centralized Control
- **Single command startup** - Start all active bots with one command
- **Bulk operations** - Stop, restart, or manage all bots at once
- **Individual management** - Control specific client bots independently
- **Health monitoring** - Automatic restart and status tracking

### Management Interface
- **Terminal commands** - Full CLI management interface
- **REST API** - HTTP endpoints for programmatic control
- **Status monitoring** - Real-time system and bot status
- **Configuration management** - Easy client and subscription management

## 📋 Requirements

- Node.js 16.x or higher
- Discord.js 13.x
- Valid Discord bot tokens for each client

## 🛠️ Installation

1. **Clone or download the project**
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Set up configuration files** (see Configuration section)

## ⚙️ Configuration

### Initial Setup

1. **Copy example configuration files**:
   ```bash
   cp config/clients.example.json config/clients.json
   cp config/subscriptions.example.json config/subscriptions.json
   ```

2. **Add your first client**:
   ```bash
   npm run manage-client add "YOUR_BOT_TOKEN_HERE" my_first_client --name "My Bot" --plan premium
   ```

### Configuration Files

#### `config/clients.json`
Stores client configurations and bot tokens (encrypted):
- Client IDs and names
- Bot tokens (automatically encrypted)
- Prefixes and settings
- Owner and guild configurations

#### `config/subscriptions.json`
Manages subscription data:
- Subscription plans and features
- Payment status and expiration dates
- Guild limits and usage tracking
- Plan definitions and pricing

## 🎮 Usage

### Starting the System

#### Option 1: Management Server Only
```bash
node multi-tenant-bot.js
```
Starts the management server without auto-starting bots.

#### Option 2: Auto-start All Bots
```bash
node multi-tenant-bot.js --auto-start
```
Starts the management server and automatically starts all active bots.

#### Option 3: Start All Bots (Script)
```bash
npm run start-all
```
Dedicated script for starting all active bot instances with health monitoring.

### Management Commands

#### Client Management
```bash
# Add a new client
npm run manage-client add "BOT_TOKEN" client_id --name "Client Name" --plan premium

# Remove a client
npm run manage-client remove client_id --force

# List all clients
npm run list-clients

# Show client status
npm run client-status client_id

# Show system status
npm run client-status
```

#### Bot Control
```bash
# Start specific bot
npm run manage-client start client_id

# Stop specific bot
npm run manage-client stop client_id

# Restart specific bot
npm run manage-client restart client_id

# Start all active bots
npm run start-all

# Stop all running bots
npm run stop-all
```

#### Subscription Management
```bash
# Activate subscription
npm run manage-client subscription client_id activate

# Suspend subscription
npm run manage-client subscription client_id suspend "Reason here"

# Change subscription plan
npm run manage-client subscription client_id plan enterprise

# Delete subscription
npm run manage-client subscription client_id delete
```

### REST API Endpoints

The management server provides HTTP endpoints for programmatic control:

#### System Status
- `GET /` - Health check and basic status
- `GET /status` - Detailed system status
- `GET /bot/:clientId` - Specific client information

#### Bot Control
- `POST /bot/:clientId/start` - Start specific bot
- `POST /bot/:clientId/stop` - Stop specific bot
- `POST /bots/start-all` - Start all active bots
- `POST /bots/stop-all` - Stop all running bots

#### Example API Usage
```bash
# Check system status
curl http://localhost:3000/status

# Start a specific bot
curl -X POST http://localhost:3000/bot/my_client_1/start

# Start all bots
curl -X POST http://localhost:3000/bots/start-all
```

## 🏗️ Architecture

### Core Components

#### `manager/BotManager.js`
- Creates and manages Discord client instances
- Handles bot lifecycle (start, stop, restart)
- Provides health monitoring and status tracking
- Manages graceful shutdown procedures

#### `manager/SubscriptionManager.js`
- Handles subscription plans and billing status
- Enforces access control and feature permissions
- Manages guild limits and usage tracking
- Provides subscription analytics and reporting

#### `manager/TokenManager.js`
- Securely stores and manages bot tokens
- Handles client configurations and settings
- Provides token encryption and validation
- Manages client lifecycle and metadata

#### `handler/index.js`
- Enhanced command and event handler
- Provides client-specific data isolation
- Loads commands and events for each bot instance
- Maintains compatibility with existing bot code

### Data Isolation

Each client's data is isolated using prefixed database keys:
```javascript
// Client-specific data storage
client.db.get('user_points') // Stored as: client_[clientId]_user_points
client.db.set('guild_config', data) // Stored as: client_[clientId]_guild_config
```

### Security Features

- **Token encryption** - All bot tokens are encrypted at rest
- **Access control** - Subscription-based feature enforcement
- **Input validation** - Token format and configuration validation
- **Error isolation** - Client errors don't affect other instances

## 📊 Monitoring and Logging

### Health Monitoring
- Automatic bot restart on failure
- Regular health checks every 60 seconds
- Status tracking and reporting
- Performance metrics collection

### Logging
- Structured logging with timestamps
- Client-specific log identification
- Error tracking and reporting
- System event logging

## 🔧 Troubleshooting

### Common Issues

#### Bot Won't Start
1. Check subscription status: `npm run client-status client_id`
2. Verify token validity
3. Check console logs for specific errors
4. Ensure subscription is active

#### Database Issues
1. Check file permissions in `config/` directory
2. Verify JSON file syntax
3. Backup and recreate configuration files if corrupted

#### Memory Issues
1. Monitor bot count and system resources
2. Implement bot restart schedules if needed
3. Check for memory leaks in custom commands

### Debug Mode
Enable verbose logging:
```bash
npm run start-all -- --verbose
npm run manage-client status --verbose
```

## 🔄 Backup and Recovery

### Configuration Backup
```bash
# Backup configuration files
cp config/clients.json config/clients.backup.json
cp config/subscriptions.json config/subscriptions.backup.json
```

### Database Backup
The system uses `pro.db` for data storage. Regular backups of the database file are recommended.

## 🚦 Production Deployment

### Environment Variables
```bash
PORT=3000                    # Management server port
NODE_ENV=production         # Production mode
MAX_CLIENTS=100            # Maximum client limit
```

### Process Management
Use PM2 or similar process managers for production:
```bash
pm2 start multi-tenant-bot.js --name "bot-manager"
pm2 start scripts/start-all.js --name "bot-instances"
```

### Security Considerations
- Use environment variables for sensitive configuration
- Implement proper access controls for the management API
- Regular security updates and monitoring
- Network security and firewall configuration

## 📈 Scaling

### Horizontal Scaling
- Deploy multiple instances with load balancing
- Use shared database for configuration synchronization
- Implement distributed bot management

### Performance Optimization
- Monitor memory usage per bot instance
- Implement connection pooling
- Use clustering for CPU-intensive operations
- Regular performance profiling and optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🚀 Quick Start

### 1. Setup Script
Run the automated setup:
```bash
node setup.js
```
This will:
- Create necessary directories
- Copy example configurations
- Guide you through adding your first client
- Test the system setup

### 2. Manual Setup
If you prefer manual setup, follow the Configuration section above.

## 📄 License

This project is licensed under the ISC License - see the package.json file for details.

## 🆘 Support

For support and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Create an issue with detailed information
4. Include system status and configuration (without tokens)

---

**Note**: Always keep your bot tokens secure and never share them publicly. The system encrypts tokens automatically, but additional security measures are recommended for production use.
