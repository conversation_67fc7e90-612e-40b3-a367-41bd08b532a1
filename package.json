{"name": "test", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "setup": "node setup.js", "fix-token": "node fix-token-validation.js", "fix-commands": "node fix-commands-database.js", "migrate": "node migrate-to-multitenant.js", "multi-tenant": "node multi-tenant-bot.js", "start-all": "node scripts/start-all.js", "stop-all": "node scripts/stop-all.js", "manage-client": "node scripts/manage-client.js", "add-client": "node scripts/manage-client.js add", "remove-client": "node scripts/manage-client.js remove", "client-status": "node scripts/manage-client.js status", "list-clients": "node scripts/manage-client.js list"}, "author": "", "license": "ISC", "dependencies": {"@discordjs/voice": "^0.16.0", "@napi-rs/canvas": "^0.1.44", "adm-zip": "^0.5.12", "all": "^0.0.0", "canvas": "^2.11.2", "canvas-constructor": "^7.0.1", "cloudinary": "^2.0.3", "config": "^3.3.9", "deepai": "^1.0.23", "directus": "^10.6.3", "discord-html-transcripts": "^2.2.8", "discord-image-generation": "^1.4.25", "discord-inviter": "^0.9.3", "discord.js": "^13.17.1", "editor-canvas": "^0.2.3", "emojione": "^4.5.0", "express": "^4.18.2", "get-image-colors": "^4.0.1", "humanize-duration": "^3.31.0", "is-image-url": "^1.1.8", "javascript-obfuscator": "^4.1.0", "jimp": "^0.22.10", "json-bigint": "^1.0.0", "moment": "^2.29.4", "node-fetch": "^2.6.1", "pretty-ms": "^8.0.0", "pro.db": "^3.0.8", "remove.bg": "^1.3.0", "sobel": "^0.0.11", "valid-url": "^1.0.9"}}