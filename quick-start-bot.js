#!/usr/bin/env node

// Quick Start Bot Script
// Starts a single bot instance without the Express server conflict

const BotManager = require('./manager/BotManager');

class QuickStartBot {
    constructor() {
        this.botManager = new BotManager();
        this.setupSignalHandlers();
    }

    /**
     * Setup signal handlers for graceful shutdown
     */
    setupSignalHandlers() {
        process.on('SIGINT', this.handleShutdown.bind(this));
        process.on('SIGTERM', this.handleShutdown.bind(this));
        process.on('uncaughtException', this.handleError.bind(this));
        process.on('unhandledRejection', this.handleError.bind(this));
    }

    /**
     * Handle graceful shutdown
     */
    async handleShutdown(signal) {
        console.log(`\n🔄 Received ${signal}. Shutting down...`);
        try {
            await this.botManager.shutdown();
            console.log('✅ Bot shut down successfully');
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }

    /**
     * Handle uncaught errors
     */
    handleError(error) {
        console.error('❌ Uncaught error:', error);
    }

    /**
     * Start the first available bot
     */
    async startFirstBot() {
        try {
            console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Quick Bot Starter                         ║
║                  Starting First Available Bot               ║
╚══════════════════════════════════════════════════════════════╝
            `);

            // Get active clients
            const activeClients = this.botManager.subscriptionManager.getActiveClients();
            
            if (activeClients.length === 0) {
                console.log('❌ No active clients found. Please add a client first:');
                console.log('   npm run fix-token');
                console.log('   or');
                console.log('   npm run setup');
                return;
            }

            console.log(`📊 Found ${activeClients.length} active client(s)`);
            
            // Start the first client
            const clientId = activeClients[0];
            console.log(`🚀 Starting bot for client: ${clientId}`);
            
            const success = await this.botManager.startBot(clientId);
            
            if (success) {
                console.log(`✅ Bot started successfully for client: ${clientId}`);
                console.log('\n🎮 Bot is now online and ready to receive commands!');
                console.log('   Try sending: #help');
                console.log('\n⚠️  Press Ctrl+C to stop the bot');
                
                // Keep the process alive
                process.stdin.resume();
                
            } else {
                console.log(`❌ Failed to start bot for client: ${clientId}`);
                console.log('Please check the logs above for error details.');
            }

        } catch (error) {
            console.error('❌ Failed to start bot:', error);
            process.exit(1);
        }
    }

    /**
     * Start all available bots
     */
    async startAllBots() {
        try {
            console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Quick Bot Starter                         ║
║                   Starting All Available Bots               ║
╚══════════════════════════════════════════════════════════════╝
            `);

            const results = await this.botManager.startAllBots();
            
            if (results.success.length > 0) {
                console.log(`✅ Started ${results.success.length} bot(s) successfully`);
                results.success.forEach(clientId => {
                    console.log(`   • ${clientId}`);
                });
                
                console.log('\n🎮 Bots are now online and ready to receive commands!');
                console.log('   Try sending: #help');
                console.log('\n⚠️  Press Ctrl+C to stop all bots');
                
                // Keep the process alive
                process.stdin.resume();
                
            } else {
                console.log('❌ No bots were started successfully');
                if (results.failed.length > 0) {
                    console.log('Failed clients:');
                    results.failed.forEach(clientId => {
                        console.log(`   • ${clientId}`);
                    });
                }
            }

        } catch (error) {
            console.error('❌ Failed to start bots:', error);
            process.exit(1);
        }
    }

    /**
     * Show status of all clients
     */
    showStatus() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Bot System Status                         ║
╚══════════════════════════════════════════════════════════════╝
        `);

        const status = this.botManager.getStatus();
        const subStats = this.botManager.subscriptionManager.getStatistics();
        const tokenStats = this.botManager.tokenManager.getStatistics();

        console.log('📊 System Overview:');
        console.log(`   Total Clients: ${tokenStats.totalClients}`);
        console.log(`   Active Subscriptions: ${subStats.active}`);
        console.log(`   Running Bots: ${status.runningBots}`);
        console.log(`   Available Slots: ${tokenStats.availableSlots}`);

        if (status.activeClients.length > 0) {
            console.log('\n🟢 Active Clients:');
            status.activeClients.forEach(clientId => {
                const isRunning = status.runningBots.includes(clientId);
                console.log(`   • ${clientId} ${isRunning ? '(Running)' : '(Stopped)'}`);
            });
        }

        if (status.notRunning.length > 0) {
            console.log('\n🔴 Not Running:');
            status.notRunning.forEach(clientId => {
                console.log(`   • ${clientId}`);
            });
        }

        console.log('\n💡 Available Commands:');
        console.log('   node quick-start-bot.js --first    # Start first available bot');
        console.log('   node quick-start-bot.js --all      # Start all available bots');
        console.log('   node quick-start-bot.js --status   # Show this status');
    }

    /**
     * Main execution
     */
    async run() {
        const args = process.argv.slice(2);

        if (args.includes('--help') || args.includes('-h')) {
            console.log(`
Quick Bot Starter

Usage: node quick-start-bot.js [options]

Options:
  --first           Start the first available bot
  --all             Start all available bots
  --status          Show system status
  --help, -h        Show this help message

Examples:
  node quick-start-bot.js --first
  node quick-start-bot.js --all
  node quick-start-bot.js --status

This tool bypasses the Express server to avoid port conflicts.
            `);
            return;
        }

        if (args.includes('--status')) {
            this.showStatus();
            return;
        }

        if (args.includes('--all')) {
            await this.startAllBots();
            return;
        }

        // Default: start first bot
        await this.startFirstBot();
    }
}

// Run the script
if (require.main === module) {
    const starter = new QuickStartBot();
    starter.run().catch(error => {
        console.error('❌ Quick start failed:', error);
        process.exit(1);
    });
}

module.exports = QuickStartBot;
