// Multi-tenant Bot Manager
// Manages multiple Discord bot instances with isolated configurations

const { Client, Collection } = require("discord.js");
const fs = require('fs');
const path = require('path');
const SubscriptionManager = require('./SubscriptionManager');
const TokenManager = require('./TokenManager');

class BotManager {
    constructor() {
        this.bots = new Map(); // clientId -> bot instance
        this.subscriptionManager = new SubscriptionManager();
        this.tokenManager = new TokenManager();
        this.isShuttingDown = false;
    }

    /**
     * Create and configure a new bot instance
     * @param {string} clientId - Unique client identifier
     * @param {Object} config - Client configuration
     * @returns {Client} Discord client instance
     */
    createBotInstance(clientId, config) {
        const client = new Client({ intents: 32767 });
        
        // Attach client-specific properties
        client.clientId = clientId;
        client.config = config;
        client.commands = new Collection();
        client.slashCommands = new Collection();
        client.prefix = config.prefix || '#';
        
        // Set up error handling
        client.on('error', (error) => {
            console.error(`❌ Bot error for client ${clientId}:`, error);
        });

        client.on('warn', (warning) => {
            console.warn(`⚠️ Bot warning for client ${clientId}:`, warning);
        });

        client.on('ready', () => {
            console.log(`✅ Bot ready for client: ${clientId} (${client.user.tag})`);
        });

        return client;
    }

    /**
     * Start a bot instance for a specific client
     * @param {string} clientId - Client identifier
     * @returns {Promise<boolean>} Success status
     */
    async startBot(clientId) {
        try {
            // Check subscription status
            if (!this.subscriptionManager.isClientActive(clientId)) {
                console.log(`❌ Cannot start bot for client ${clientId}: Subscription inactive`);
                return false;
            }

            // Get client configuration
            const config = this.tokenManager.getClientConfig(clientId);
            if (!config || !config.token) {
                console.log(`❌ Cannot start bot for client ${clientId}: No valid configuration`);
                return false;
            }

            // Check if bot is already running
            if (this.bots.has(clientId)) {
                console.log(`⚠️ Bot for client ${clientId} is already running`);
                return true;
            }

            // Create and configure bot instance
            const client = this.createBotInstance(clientId, config);
            
            // Load handler
            const handlerPath = path.join(process.cwd(), 'handler');
            if (fs.existsSync(handlerPath)) {
                await require(handlerPath)(client);
            }

            // Login to Discord
            await client.login(config.token);
            
            // Store bot instance
            this.bots.set(clientId, client);
            
            console.log(`🚀 Bot started successfully for client: ${clientId}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to start bot for client ${clientId}:`, error);
            return false;
        }
    }

    /**
     * Stop a bot instance for a specific client
     * @param {string} clientId - Client identifier
     * @returns {Promise<boolean>} Success status
     */
    async stopBot(clientId) {
        try {
            const client = this.bots.get(clientId);
            if (!client) {
                console.log(`⚠️ No bot running for client ${clientId}`);
                return true;
            }

            // Gracefully disconnect
            await client.destroy();
            this.bots.delete(clientId);
            
            console.log(`🛑 Bot stopped for client: ${clientId}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to stop bot for client ${clientId}:`, error);
            return false;
        }
    }

    /**
     * Start all active bot instances
     * @returns {Promise<Object>} Results summary
     */
    async startAllBots() {
        const activeClients = this.subscriptionManager.getActiveClients();
        const results = { success: [], failed: [] };

        console.log(`🚀 Starting ${activeClients.length} bot instances...`);

        for (const clientId of activeClients) {
            const success = await this.startBot(clientId);
            if (success) {
                results.success.push(clientId);
            } else {
                results.failed.push(clientId);
            }
        }

        console.log(`✅ Started ${results.success.length} bots successfully`);
        if (results.failed.length > 0) {
            console.log(`❌ Failed to start ${results.failed.length} bots: ${results.failed.join(', ')}`);
        }

        return results;
    }

    /**
     * Stop all running bot instances
     * @returns {Promise<Object>} Results summary
     */
    async stopAllBots() {
        this.isShuttingDown = true;
        const runningClients = Array.from(this.bots.keys());
        const results = { success: [], failed: [] };

        console.log(`🛑 Stopping ${runningClients.length} bot instances...`);

        for (const clientId of runningClients) {
            const success = await this.stopBot(clientId);
            if (success) {
                results.success.push(clientId);
            } else {
                results.failed.push(clientId);
            }
        }

        console.log(`✅ Stopped ${results.success.length} bots successfully`);
        if (results.failed.length > 0) {
            console.log(`❌ Failed to stop ${results.failed.length} bots: ${results.failed.join(', ')}`);
        }

        this.isShuttingDown = false;
        return results;
    }

    /**
     * Restart a specific bot instance
     * @param {string} clientId - Client identifier
     * @returns {Promise<boolean>} Success status
     */
    async restartBot(clientId) {
        console.log(`🔄 Restarting bot for client: ${clientId}`);
        await this.stopBot(clientId);
        return await this.startBot(clientId);
    }

    /**
     * Get status of all bot instances
     * @returns {Object} Status information
     */
    getStatus() {
        const activeClients = this.subscriptionManager.getActiveClients();
        const runningBots = Array.from(this.bots.keys());
        
        return {
            totalActiveClients: activeClients.length,
            runningBots: runningBots.length,
            activeClients,
            runningBots,
            notRunning: activeClients.filter(id => !runningBots.includes(id)),
            runningButInactive: runningBots.filter(id => !activeClients.includes(id))
        };
    }

    /**
     * Get a specific bot instance
     * @param {string} clientId - Client identifier
     * @returns {Client|null} Bot instance or null
     */
    getBot(clientId) {
        return this.bots.get(clientId) || null;
    }

    /**
     * Check if a bot is running for a client
     * @param {string} clientId - Client identifier
     * @returns {boolean} Running status
     */
    isBotRunning(clientId) {
        return this.bots.has(clientId);
    }

    /**
     * Graceful shutdown of all bots
     */
    async shutdown() {
        console.log('🔄 Initiating graceful shutdown...');
        await this.stopAllBots();
        console.log('✅ All bots shut down successfully');
    }
}

module.exports = BotManager;
