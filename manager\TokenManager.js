// Token and Client Configuration Manager
// Manages bot tokens, client configurations, and security

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class TokenManager {
    constructor() {
        this.clientsFile = path.join(process.cwd(), 'config', 'clients.json');
        this.clients = this.loadClients();
    }

    /**
     * Load client configurations from file
     * @returns {Object} Clients data
     */
    loadClients() {
        try {
            if (fs.existsSync(this.clientsFile)) {
                const data = fs.readFileSync(this.clientsFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.error('Error loading clients:', error);
        }
        
        // Return default structure if file doesn't exist or is corrupted
        return {
            clients: {},
            settings: {
                maxClients: 100,
                defaultPrefix: '#',
                encryptTokens: true
            }
        };
    }

    /**
     * Save client configurations to file
     */
    saveClients() {
        try {
            const configDir = path.dirname(this.clientsFile);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }
            fs.writeFileSync(this.clientsFile, JSON.stringify(this.clients, null, 4));
        } catch (error) {
            console.error('Error saving clients:', error);
        }
    }

    /**
     * Encrypt sensitive data
     * @param {string} text - Text to encrypt
     * @returns {string} Encrypted text
     */
    encrypt(text) {
        if (!this.clients.settings.encryptTokens) return text;
        
        const algorithm = 'aes-256-cbc';
        const key = crypto.scryptSync('bot-manager-secret', 'salt', 32);
        const iv = crypto.randomBytes(16);
        
        const cipher = crypto.createCipher(algorithm, key);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        return iv.toString('hex') + ':' + encrypted;
    }

    /**
     * Decrypt sensitive data
     * @param {string} encryptedText - Encrypted text
     * @returns {string} Decrypted text
     */
    decrypt(encryptedText) {
        if (!this.clients.settings.encryptTokens) return encryptedText;
        
        try {
            const algorithm = 'aes-256-cbc';
            const key = crypto.scryptSync('bot-manager-secret', 'salt', 32);
            
            const textParts = encryptedText.split(':');
            const iv = Buffer.from(textParts.shift(), 'hex');
            const encryptedData = textParts.join(':');
            
            const decipher = crypto.createDecipher(algorithm, key);
            let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('Error decrypting token:', error);
            return encryptedText; // Return as-is if decryption fails
        }
    }

    /**
     * Add a new client configuration
     * @param {string} clientId - Unique client identifier
     * @param {Object} config - Client configuration
     * @returns {boolean} Success status
     */
    addClient(clientId, config) {
        if (this.clients.clients[clientId]) {
            console.error(`❌ Client ${clientId} already exists`);
            return false;
        }

        if (Object.keys(this.clients.clients).length >= this.clients.settings.maxClients) {
            console.error(`❌ Maximum client limit (${this.clients.settings.maxClients}) reached`);
            return false;
        }

        if (!config.token) {
            console.error(`❌ Token is required for client ${clientId}`);
            return false;
        }

        const clientConfig = {
            clientId,
            token: this.encrypt(config.token),
            name: config.name || `Client ${clientId}`,
            prefix: config.prefix || this.clients.settings.defaultPrefix,
            owners: config.owners || [],
            guilds: config.guilds || [],
            features: config.features || [],
            createdAt: new Date().toISOString(),
            lastActive: null,
            status: 'inactive',
            ...config
        };

        // Remove the plain token from config to avoid storing it twice
        delete clientConfig.token;
        clientConfig.token = this.encrypt(config.token);

        this.clients.clients[clientId] = clientConfig;
        this.saveClients();
        
        console.log(`✅ Added client: ${clientId}`);
        return true;
    }

    /**
     * Update client configuration
     * @param {string} clientId - Client identifier
     * @param {Object} updates - Updates to apply
     * @returns {boolean} Success status
     */
    updateClient(clientId, updates) {
        if (!this.clients.clients[clientId]) {
            console.error(`❌ Client ${clientId} not found`);
            return false;
        }

        // Encrypt token if being updated
        if (updates.token) {
            updates.token = this.encrypt(updates.token);
        }

        this.clients.clients[clientId] = {
            ...this.clients.clients[clientId],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.saveClients();
        console.log(`✅ Updated client: ${clientId}`);
        return true;
    }

    /**
     * Remove a client configuration
     * @param {string} clientId - Client identifier
     * @returns {boolean} Success status
     */
    removeClient(clientId) {
        if (!this.clients.clients[clientId]) {
            console.error(`❌ Client ${clientId} not found`);
            return false;
        }

        delete this.clients.clients[clientId];
        this.saveClients();
        
        console.log(`✅ Removed client: ${clientId}`);
        return true;
    }

    /**
     * Get client configuration
     * @param {string} clientId - Client identifier
     * @returns {Object|null} Client configuration with decrypted token
     */
    getClientConfig(clientId) {
        const client = this.clients.clients[clientId];
        if (!client) return null;

        // Return config with decrypted token
        return {
            ...client,
            token: this.decrypt(client.token)
        };
    }

    /**
     * Get all client IDs
     * @returns {Array} Array of client IDs
     */
    getAllClientIds() {
        return Object.keys(this.clients.clients);
    }

    /**
     * Get client configurations (without tokens)
     * @returns {Object} Clients data without sensitive information
     */
    getClientsInfo() {
        const clientsInfo = {};
        
        Object.entries(this.clients.clients).forEach(([clientId, config]) => {
            clientsInfo[clientId] = {
                ...config,
                token: '***HIDDEN***' // Hide token for security
            };
        });

        return {
            clients: clientsInfo,
            settings: this.clients.settings,
            totalClients: Object.keys(this.clients.clients).length
        };
    }

    /**
     * Validate token format
     * @param {string} token - Discord bot token
     * @returns {boolean} Valid status
     */
    validateToken(token) {
        // Basic Discord token format validation
        const tokenRegex = /^[A-Za-z0-9_-]{24}\.[A-Za-z0-9_-]{6}\.[A-Za-z0-9_-]{27}$/;
        return tokenRegex.test(token);
    }

    /**
     * Update client status
     * @param {string} clientId - Client identifier
     * @param {string} status - New status
     * @returns {boolean} Success status
     */
    updateClientStatus(clientId, status) {
        return this.updateClient(clientId, {
            status,
            lastActive: new Date().toISOString()
        });
    }

    /**
     * Get clients by status
     * @param {string} status - Status to filter by
     * @returns {Array} Array of client IDs
     */
    getClientsByStatus(status) {
        return Object.entries(this.clients.clients)
            .filter(([clientId, config]) => config.status === status)
            .map(([clientId]) => clientId);
    }

    /**
     * Check if client exists
     * @param {string} clientId - Client identifier
     * @returns {boolean} Exists status
     */
    clientExists(clientId) {
        return !!this.clients.clients[clientId];
    }

    /**
     * Generate unique client ID
     * @param {string} prefix - Optional prefix
     * @returns {string} Unique client ID
     */
    generateClientId(prefix = 'client') {
        let clientId;
        do {
            const randomSuffix = crypto.randomBytes(4).toString('hex');
            clientId = `${prefix}_${randomSuffix}`;
        } while (this.clientExists(clientId));
        
        return clientId;
    }

    /**
     * Update settings
     * @param {Object} newSettings - New settings
     * @returns {boolean} Success status
     */
    updateSettings(newSettings) {
        this.clients.settings = {
            ...this.clients.settings,
            ...newSettings
        };
        
        this.saveClients();
        console.log('✅ Settings updated');
        return true;
    }

    /**
     * Get statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        const clients = Object.values(this.clients.clients);
        const statusCounts = {};
        
        clients.forEach(client => {
            statusCounts[client.status] = (statusCounts[client.status] || 0) + 1;
        });

        return {
            totalClients: clients.length,
            maxClients: this.clients.settings.maxClients,
            availableSlots: this.clients.settings.maxClients - clients.length,
            statusDistribution: statusCounts,
            encryptionEnabled: this.clients.settings.encryptTokens
        };
    }
}

module.exports = TokenManager;
