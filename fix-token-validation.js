#!/usr/bin/env node

// Quick fix for token validation issue
// This script will test your token and add it directly if valid

const readline = require('readline');

class TokenFix {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * Ask a question and return the answer
     */
    async ask(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * Improved token validation
     */
    validateToken(token) {
        // Check basic structure (3 parts separated by dots)
        const parts = token.split('.');
        if (parts.length !== 3) {
            console.log('❌ Token must have 3 parts separated by dots');
            return false;
        }

        // Check each part contains only valid characters
        const validChars = /^[A-Za-z0-9_-]+$/;
        for (let i = 0; i < parts.length; i++) {
            if (!validChars.test(parts[i])) {
                console.log(`❌ Part ${i + 1} contains invalid characters`);
                return false;
            }
        }

        // Check minimum lengths (more flexible than before)
        if (parts[0].length < 15) {
            console.log('❌ First part too short');
            return false;
        }
        if (parts[1].length < 5) {
            console.log('❌ Second part too short');
            return false;
        }
        if (parts[2].length < 20) {
            console.log('❌ Third part too short');
            return false;
        }

        console.log('✅ Token format appears valid');
        return true;
    }

    /**
     * Add client directly
     */
    async addClientDirect(token, clientId, clientName, plan) {
        try {
            const TokenManager = require('./manager/TokenManager');
            const SubscriptionManager = require('./manager/SubscriptionManager');
            
            const tokenManager = new TokenManager();
            const subscriptionManager = new SubscriptionManager();

            // Add client configuration
            const clientConfig = {
                token,
                name: clientName,
                prefix: '#',
                owners: [],
                features: []
            };

            console.log('🔄 Adding client configuration...');
            const tokenSuccess = tokenManager.addClient(clientId, clientConfig);
            if (!tokenSuccess) {
                console.log('❌ Failed to add client configuration');
                return false;
            }

            // Create subscription
            const subscriptionData = {
                plan,
                status: 'active'
            };

            console.log('🔄 Creating subscription...');
            const subscriptionSuccess = subscriptionManager.createSubscription(clientId, subscriptionData);
            if (!subscriptionSuccess) {
                tokenManager.removeClient(clientId);
                console.log('❌ Failed to create subscription');
                return false;
            }

            console.log('✅ Client added successfully!');
            console.log(`   Client ID: ${clientId}`);
            console.log(`   Name: ${clientName}`);
            console.log(`   Plan: ${plan}`);
            
            return true;

        } catch (error) {
            console.log('❌ Error adding client:', error.message);
            return false;
        }
    }

    /**
     * Main process
     */
    async run() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    Token Validation Fix                     ║
║              Direct Client Addition Tool                    ║
╚══════════════════════════════════════════════════════════════╝

This tool will help you add your bot token directly, bypassing
the strict validation that was causing issues.
        `);

        try {
            // Get token
            const token = await this.ask('\nEnter your Discord bot token: ');
            if (!token) {
                console.log('❌ Token cannot be empty');
                return;
            }

            console.log('\n🔍 Validating token format...');
            const isValid = this.validateToken(token);
            
            if (!isValid) {
                const forceAdd = await this.ask('\nToken format seems unusual. Continue anyway? (y/n): ');
                if (forceAdd.toLowerCase() !== 'y' && forceAdd.toLowerCase() !== 'yes') {
                    console.log('Operation cancelled.');
                    return;
                }
            }

            // Get client details
            const clientId = await this.ask('Enter client ID (or press Enter for auto-generated): ') || 
                            `client_${Math.random().toString(36).substr(2, 8)}`;
            
            const clientName = await this.ask('Enter client name: ') || `Client ${clientId}`;
            
            console.log('\nAvailable plans:');
            console.log('  1. basic - 1 guild, basic features');
            console.log('  2. premium - 5 guilds, advanced features');
            console.log('  3. enterprise - unlimited guilds, all features');
            
            let plan = 'premium'; // Default
            const planChoice = await this.ask('Choose plan (1-3, default: premium): ');
            if (planChoice === '1' || planChoice.toLowerCase() === 'basic') {
                plan = 'basic';
            } else if (planChoice === '3' || planChoice.toLowerCase() === 'enterprise') {
                plan = 'enterprise';
            }

            // Add client
            console.log('\n🚀 Adding client...');
            const success = await this.addClientDirect(token, clientId, clientName, plan);
            
            if (success) {
                console.log('\n🎉 Client added successfully!');
                console.log('\nNext steps:');
                console.log('  1. Start the bot:');
                console.log(`     npm run manage-client start ${clientId}`);
                console.log('  2. Or start all bots:');
                console.log('     npm run start-all');
                console.log('  3. Check status:');
                console.log('     npm run list-clients');
            } else {
                console.log('\n❌ Failed to add client. Please check the error messages above.');
            }

        } catch (error) {
            console.error('\n❌ Error:', error.message);
        } finally {
            this.rl.close();
        }
    }
}

// Run the fix tool
if (require.main === module) {
    const fix = new TokenFix();
    fix.run().catch(error => {
        console.error('❌ Fix tool failed:', error);
        process.exit(1);
    });
}

module.exports = TokenFix;
