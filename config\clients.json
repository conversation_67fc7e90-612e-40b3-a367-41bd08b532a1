{"clients": {"client_example1": {"clientId": "client_example1", "token": "encrypted_token_here", "name": "Example Client 1", "prefix": "#", "owners": ["123456789012345678"], "guilds": ["987654321098765432"], "features": ["basic_commands", "moderation"], "createdAt": "2024-01-01T00:00:00.000Z", "lastActive": null, "status": "inactive"}, "client_example2": {"clientId": "client_example2", "token": "encrypted_token_here", "name": "Example Client 2", "prefix": "!", "owners": ["123456789012345678"], "guilds": [], "features": ["basic_commands", "moderation", "advanced_features"], "createdAt": "2024-01-01T00:00:00.000Z", "lastActive": null, "status": "inactive"}, "704536642815524906": {"clientId": "704536642815524906", "name": "Oryn store", "prefix": "#", "owners": [], "guilds": [], "features": [], "createdAt": "2025-06-05T03:30:47.748Z", "lastActive": null, "status": "inactive", "token": "5f5fb7180d7edd8ef3c122a7da476543:36ae60f01bcdf7739bf22645168f5038d8fb05fa616e1e343dca184bf9007d2d3d2621a9939d6bc0cad02735b5bb0ac60247dd5083ed9c7db5d454e6f326d0a5d0557fd143bb1aee662ca5b9443b19b1"}}, "settings": {"maxClients": 100, "defaultPrefix": "#", "encryptTokens": true}}