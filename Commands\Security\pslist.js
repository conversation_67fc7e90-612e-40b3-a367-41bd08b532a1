const db = require("pro.db");
const { MessageEmbed } = require("discord.js");
const { prefix, owners } = require(`${process.cwd()}/config`);


module.exports = {
  name: 'pslist',
  run: async (client, message) => {

    if (!owners.includes(message.author.id)) return message.react('❌');

    if (!message.guild) return;
    const Color = clientDb.get(`Guild_Color = ${message.guild.id}`) || '#4e464f';
    if (!Color) return;


    const antibotsStatus = clientDb.get(`antibots-${message.guild.id}`) === 'on' ? 'مُفعل' : 'مُغلق';
    const anticreateStatus = clientDb.get(`anticreate-${message.guild.id}`) === true ? 'مُفعل' : 'مُغلق';
    const antideleteStatus = clientDb.get(`antiDelete-${message.guild.id}`) === true ? 'مُفعل' : 'مُغلق';
    const antijoinStatus = await clientDb.get(`antijoinEnabled_${message.guild.id}`) === true ? 'مُفعل' : 'مُغلق';
    const antilinksStatus = clientDb.get(`antilinks-${message.guild.id}`) === 'on' ? 'مُفعل' : 'مُغلق'; // New line
    const antispamStatus = clientDb.get(`spamProtectionEnabled_${message.guild.id}`) === true ? 'مُفعل' : 'مُغلق'; // New line


    const embed = new MessageEmbed()
   .setColor(`${Color || `#4e464f`}`)
    .setDescription(`\`#1\` Antibots: ${antibotsStatus}\n\`#2\` Anticreate: ${anticreateStatus}\n\`#3\` Antidelete: ${antideleteStatus}\n\`#4\` Antijoin: ${antijoinStatus}\n\`#5\` AntiLinks: ${antilinksStatus}\n\`#6\` AntiLinks: ${antilinksStatus}\n\`#7\` AntiSpam: ${antispamStatus}`);
   message.reply({ embeds: [embed] });


  }
}
