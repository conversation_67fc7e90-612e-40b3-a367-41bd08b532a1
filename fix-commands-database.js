#!/usr/bin/env node

// Fix Commands Database Usage
// Updates all command files to use client.db instead of direct Data access

const fs = require('fs');
const path = require('path');

class CommandDatabaseFixer {
    constructor() {
        this.fixedFiles = [];
        this.errorFiles = [];
    }

    /**
     * Get all JavaScript files in a directory recursively
     */
    getJSFiles(dir) {
        let files = [];
        
        if (!fs.existsSync(dir)) {
            return files;
        }

        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                files = files.concat(this.getJSFiles(fullPath));
            } else if (item.endsWith('.js')) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    /**
     * Fix a single command file
     */
    fixCommandFile(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            let modified = false;

            // Check if file needs fixing
            if (!content.includes('const Data = require') && !content.includes('const db = require')) {
                return false; // No database usage found
            }

            // Add client.db fallback at the beginning of run function
            const runFunctionRegex = /run\s*:\s*\(client,\s*message,\s*args\)\s*=>\s*{/g;
            const runFunctionMatch = content.match(runFunctionRegex);
            
            if (runFunctionMatch) {
                const insertPoint = content.indexOf(runFunctionMatch[0]) + runFunctionMatch[0].length;
                const dbFallback = '\n        // استخدام client.db للعزل بين العملاء\n        const clientDb = client.db || Data || db;\n';
                
                // Check if already added
                if (!content.includes('const clientDb = client.db')) {
                    content = content.slice(0, insertPoint) + dbFallback + content.slice(insertPoint);
                    modified = true;
                }
            }

            // Replace Data.get, Data.set, etc. with clientDb
            const replacements = [
                { from: /Data\.get\(/g, to: 'clientDb.get(' },
                { from: /Data\.set\(/g, to: 'clientDb.set(' },
                { from: /Data\.fetch\(/g, to: 'clientDb.fetch(' },
                { from: /Data\.delete\(/g, to: 'clientDb.delete(' },
                { from: /Data\.has\(/g, to: 'clientDb.has(' },
                { from: /Data\.add\(/g, to: 'clientDb.add(' },
                { from: /Data\.subtract\(/g, to: 'clientDb.subtract(' },
                { from: /db\.get\(/g, to: 'clientDb.get(' },
                { from: /db\.set\(/g, to: 'clientDb.set(' },
                { from: /db\.fetch\(/g, to: 'clientDb.fetch(' },
                { from: /db\.delete\(/g, to: 'clientDb.delete(' },
                { from: /db\.has\(/g, to: 'clientDb.has(' },
                { from: /db\.add\(/g, to: 'clientDb.add(' },
                { from: /db\.subtract\(/g, to: 'clientDb.subtract(' }
            ];

            for (const replacement of replacements) {
                if (content.match(replacement.from)) {
                    content = content.replace(replacement.from, replacement.to);
                    modified = true;
                }
            }

            // Write back if modified
            if (modified) {
                fs.writeFileSync(filePath, content, 'utf8');
                this.fixedFiles.push(filePath);
                return true;
            }

            return false;

        } catch (error) {
            console.error(`Error fixing file ${filePath}:`, error.message);
            this.errorFiles.push({ file: filePath, error: error.message });
            return false;
        }
    }

    /**
     * Fix all command files
     */
    fixAllCommands() {
        console.log('🔧 Fixing command files to use client.db...\n');

        const directories = ['Commands', 'events'];
        let totalFiles = 0;

        for (const dir of directories) {
            if (fs.existsSync(dir)) {
                console.log(`📁 Processing directory: ${dir}`);
                const files = this.getJSFiles(dir);
                totalFiles += files.length;

                for (const file of files) {
                    const relativePath = path.relative(process.cwd(), file);
                    const fixed = this.fixCommandFile(file);
                    
                    if (fixed) {
                        console.log(`   ✅ Fixed: ${relativePath}`);
                    } else {
                        console.log(`   ⏭️  Skipped: ${relativePath}`);
                    }
                }
            } else {
                console.log(`⚠️  Directory not found: ${dir}`);
            }
        }

        console.log('\n📊 Summary:');
        console.log(`   Total files processed: ${totalFiles}`);
        console.log(`   Files fixed: ${this.fixedFiles.length}`);
        console.log(`   Files with errors: ${this.errorFiles.length}`);

        if (this.fixedFiles.length > 0) {
            console.log('\n✅ Fixed files:');
            this.fixedFiles.forEach(file => {
                console.log(`   • ${path.relative(process.cwd(), file)}`);
            });
        }

        if (this.errorFiles.length > 0) {
            console.log('\n❌ Files with errors:');
            this.errorFiles.forEach(({ file, error }) => {
                console.log(`   • ${path.relative(process.cwd(), file)}: ${error}`);
            });
        }

        console.log('\n🎉 Command database fix completed!');
        console.log('Now restart your bots to apply the changes:');
        console.log('   npm run stop-all');
        console.log('   npm run start-all');
    }

    /**
     * Create a backup of important files
     */
    createBackup() {
        const backupDir = `backup_commands_${Date.now()}`;
        console.log(`💾 Creating backup in: ${backupDir}`);

        try {
            fs.mkdirSync(backupDir, { recursive: true });

            const directories = ['Commands', 'events'];
            for (const dir of directories) {
                if (fs.existsSync(dir)) {
                    const backupPath = path.join(backupDir, dir);
                    this.copyDirectory(dir, backupPath);
                }
            }

            console.log(`✅ Backup created successfully in: ${backupDir}`);
            return backupDir;

        } catch (error) {
            console.error('❌ Failed to create backup:', error.message);
            return null;
        }
    }

    /**
     * Copy directory recursively
     */
    copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }

        const items = fs.readdirSync(src);
        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            const stat = fs.statSync(srcPath);

            if (stat.isDirectory()) {
                this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    /**
     * Main execution
     */
    run() {
        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                Command Database Usage Fixer                 ║
║              Multi-Tenant Compatibility Tool                ║
╚══════════════════════════════════════════════════════════════╝

This tool will update all command files to use client.db instead of
direct Data/db access, ensuring proper client isolation.
        `);

        // Create backup
        const backupDir = this.createBackup();
        if (!backupDir) {
            console.log('❌ Cannot proceed without backup. Exiting.');
            return;
        }

        // Fix all commands
        this.fixAllCommands();

        console.log(`\n💾 Backup location: ${backupDir}`);
        console.log('If anything goes wrong, you can restore from the backup.');
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Command Database Usage Fixer

Usage: node fix-commands-database.js [options]

Options:
  --help, -h        Show this help message

This script will:
  1. Create a backup of all command and event files
  2. Update all files to use client.db instead of direct Data access
  3. Ensure proper client isolation in multi-tenant environment
  4. Provide a summary of changes made

The script is safe to run multiple times.
    `);
    process.exit(0);
}

// Run the fixer
if (require.main === module) {
    const fixer = new CommandDatabaseFixer();
    fixer.run();
}

module.exports = CommandDatabaseFixer;
